package com.yhd.buc.foreign.dao;

import com.github.yulichang.base.MPJBaseMapper;
import com.yhd.buc.foreign.pojo.po.UserInfoPO;
import com.yhd.buc.foreign.pojo.vo.request.CompanyPageRequestVO;
import com.yhd.buc.foreign.pojo.vo.response.JoinCompanyUseListResponseVO;
import com.yhd.buc.foreign.pojo.vo.response.UserInfoByUserNameResponseVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UserInfoDAO extends MPJBaseMapper<UserInfoPO> {


  List<JoinCompanyUseListResponseVO> selectJoinCompanyMember(@Param("companyPageRequestVO")CompanyPageRequestVO companyPageRequestVO, @Param("roleCode")String roleCode);

  /**
   * 根据用户名模糊搜索用户信息
   * */
  List<UserInfoByUserNameResponseVO> selectUserInfoByUserName(String userName);
}
