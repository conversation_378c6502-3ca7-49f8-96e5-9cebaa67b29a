package com.yhd.buc.foreign.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yhd.buc.foreign.pojo.po.PlatformPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PlatformDao  extends BaseMapper<PlatformPO> {

    /**
     * 根据线下子公司标识查询线上平台集合
     *
     * @param companyCode 请求参数
     * @return BusinessResponse<PlatformInfoResponseVO>
     */
    List<String> getPlatformInfoCodeListBySubsidiaryCode(@Param("companyCode") String companyCode);
}