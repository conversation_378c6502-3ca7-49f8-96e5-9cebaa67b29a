package com.yhd.buc.foreign.dao;

import com.github.yulichang.base.MPJBaseMapper;
import com.yhd.buc.foreign.pojo.po.CompanyRelationUserPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户企业关联表DAO
 *
 * <AUTHOR>
 * @since 2022-08-26
 */
public interface CompanyRelationUserDAO extends MPJBaseMapper<CompanyRelationUserPO> {
    /**
     * 根据企业编码查询用户编码集合
     *
     * @param companyCode 请求参数
     * @return BusinessResponse<PlatformInfoResponseVO>
     */
    List<String> getUserCodeListByCompanyCode(@Param("companyCode") String companyCode);

}