package com.yhd.buc.foreign.exception;

import com.yhd.common.exception.BizException;
import com.yhd.common.json.JSONUtil;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: CmsGenericExceptionResolver.java, v 0.1 2022/8/12 15:27 fenggang Exp $
 * 覆盖默认的全局异常
 */
@Component
public class CmsGenericExceptionResolver implements HandlerExceptionResolver, Ordered {
    private static final int PARAMETER_ERROR_CODE = 500001;
    private static final Logger logger = LoggerFactory.getLogger(CmsGenericExceptionResolver.class);


    @Override
    public ModelAndView resolveException(HttpServletRequest request, HttpServletResponse response, Object handler, Exception e) {
        logger.info("Resolve exception, msg:{}", e.getMessage(), e);
        response.setStatus(HttpStatus.OK.value());
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Cache-Control", "no-cache, must-revalidate");
        String data;
        if (e instanceof BizException) {
            BizException bizException = (BizException) e;
            data = JSONUtil.toJson(BusinessResponse.fail(bizException.getCode(), bizException.getMessage()));
        } else if (e instanceof MaxUploadSizeExceededException) {
            data = JSONUtil.toJson(BusinessResponse.fail("文件大小超出限制"));
        } else if (e instanceof MethodArgumentNotValidException) {
            //封装处理实体类参数校验
            data = getData((MethodArgumentNotValidException) e);

        } else if (e instanceof BindException) {
            BindException bindException = (BindException) e;
            data = JSONUtil.toJson(BusinessResponse.fail(PARAMETER_ERROR_CODE, (bindException.getBindingResult().getAllErrors().get(0)).getDefaultMessage()));
        } else {
            BusinessResponse<Object> businessResponse = BusinessResponse.fail("服务异常");
            businessResponse.setData(e.getMessage());
            data = JSONUtil.toJson(businessResponse);
        }

        try {
            PrintWriter writer = response.getWriter();
            writer.write(data);
            writer.flush();
        } catch (Exception var7) {
            logger.error("Exception:", var7);
        }

        return new ModelAndView();
    }


    private String getData(MethodArgumentNotValidException validException) {
        try {
            BindingResult bindingResult = validException.getBindingResult();

            FieldError fieldError = bindingResult.getFieldErrors().get(0);
            String field = fieldError.getField();
            Object target = bindingResult.getTarget();
            if (Objects.isNull(target)) {
                return validException.getMessage();
            }
            Schema annotation = target.getClass().getDeclaredField(field).getAnnotation(Schema.class);
            String description = annotation.description();
            String title = StringUtils.isBlank(annotation.title()) ? StringUtils.EMPTY : annotation.title();
            String replactDate = StringUtils.isBlank(description) ? title : description;
            Object[] arguments = fieldError.getArguments();
            String format = String.format(Objects.requireNonNull(fieldError.getDefaultMessage()), replactDate);
            String length = Arrays.stream(arguments)
                    .filter(f -> !(f instanceof DefaultMessageSourceResolvable))
                    .map(Object::toString)
                    .filter(f->!StringUtils.equals(f,CommonConstant.STATUS_NORMAL))
                    .sorted(Comparator.naturalOrder())
                    .collect(Collectors.joining(CommonConstant.DASH));
            if (StringUtils.isNotBlank(length)) {
                format = format + CommonConstant.SYMBOL_COLON + length;
            }
            return JSONUtil.toJson(BusinessResponse.fail(PARAMETER_ERROR_CODE, format));
        } catch (NoSuchFieldException noSuchFieldException) {
            logger.error("getData noSuchFieldException is :{}",noSuchFieldException.toString());
            return noSuchFieldException.getMessage();
        }

    }

    @Override
    public int getOrder() {
        return 0;
    }
}
