package com.yhd.buc.foreign.enums;

/**
 * <AUTHOR>
 * @version Id:ConstantStatusEnum.java, v 0.12022/5/25 15:21 kangrong Exp $
 */
public enum ConstantStatusEnum {
    //基础
    SUCCESS(0, "成功", "success."),
    EXCEPTION_ERROR(540501, "异常错误", "exception error."),
    PARAM_NOT_EMPTY(540502, "参数不能为空", "param not empty."),
    PARAM_VALIDATION_ERROR(540503, "参数验证错误", "param validation error."),
    REQUEST_EXCEPTION(540504, "请求异常", "request exception."),
    REPETITIVE_OPERATION(540505, "重复操作", "repetitive operation."),
    DATA_NOT_EXIST(540506, "数据不存在", "data not exist."),
    DATA_ALREADY_EXIST(540507, "数据已存在", "data already exist."),


    //ID生成
    ID_GENERATE_FAILED(540550, "ID生成错误", "IdVO generate failed."),



    //数据库异常
    DATABASE_QUERY_EXCEPTION(500601, "查询数据库异常", "database query exception"),
    DATABASE_ADD_EXCEPTION(500602, "新增数据异常", "database add exception"),
    DATABASE_DELETE_EXCEPTION(540603, "删除数据异常", "database delete exception"),
    DATABASE_UPDATE_EXCEPTION(540604, "更新数据异常", "database update exception"),
    DATABASE_QUERY_RESULT_IS_NULL(500605, "查询数据不存在", "database query result is null"),


    //注册
    BLACKLIST_IP(540650, "此IP已在黑名单内", "IP ADDRESS IN THE BLACKLIST"),
    SEND_EMAIL_EXCEPTION(540651,"发送邮件异常","send email exception"),
    BLACKLIST_EMAIL(540652, "此邮箱已在黑名单内", "EMAIL ADDRESS IN THE BLACKLIST"),
    SENDING_FREQUENT(540653, "发送频繁，请三十秒后再试", "SENDING_FREQUENT"),
    SEND_MOBILE_MESSAGE_EXCEPTION(540654,"发送手机短信异常异常","SEND_MOBILE_MESSAGE_EXCEPTION"),
    BLACKLIST_MOBILE_NUMBER(540655, "此号码已在黑名单内", "BLACKLIST_MOBILE_NUMBER"),
    ADDRESS_NUMBER_OUT_OF_LIMIT(540651, "地址数量超过上限", "Address number out of limit"),
    ACCOUNT_HAS_BEEN_DISABLED(540652, "账户不存在或被禁用", "The account has been disabled"),
    CURRENT_PASSWORD_ERROR(540653, "当前密码输入不正确，请再试一次", "The current password did not match,Please try again"),
    SYNCHRONIZATION_SOME_PARAMETER_ERROR(540654, "同步企业数据部分参数异常，异常ERP企业编号为{}", "SYNCHRONIZATION PARAMETER ERROR"),
    SYNCHRONIZATION_ERROR(540655, "同步企业数据异常", "SYNCHRONIZATION ERROR"),
    ACCOUNT_ALREADY_EXISTS(540656, "账户已存在", "Account already exists"),
    EMAIL_FORMAT_ERROR(540657, "邮箱格式错误", "Email format error"),
    SEND_COMPANYINFO_OVERSEASERP_ERROR(540658, "发送企业数据到ERP失败", "send companyInfo overseasERP interface fail"),
    REGISTERED_FAIL(540659, "注册用户失败", "registered user fail"),
    SEND_VERIFICATION_CODE_FAIL(540660, "发送验证码失败", "send verification code fail"),
    VERIFICATION_CODE_ERROR(540661, "验证码错误", "verification code error"),
    VERIFICATION_ACCOUNT_FAIL(540662, "校验账户失败", "verification account fail"),
    THE_BUSINESS_HAS_BEEN_REGISTERED(540663, "企业已被注册", "The business has been registered"),
    REDIRECT_FRONT_END_ERROR(540664, "重定向到前端页面失败", "redirect front end error"),
    DECODE_EMAIL_ERROR(540665, "解密邮箱失败", "decode email error"),
    RESET_PASSWORD_ERROR(540666, "重置密码失败", "reset password error"),
    DECRYPTION_FAILURE(540667, "解密失败", "decryption failure"),
    QUERY_SMS_TEMPLATE_ERROR(540668, "查询sms模板异常", "query sms template error"),
    MOBILE_NUMBER_FORMAT_ERROR(540669, "手机号码格式错误", "mobile number format error"),
    SEND_DINGDING_MESSAGE_ERROR(540670, "发送钉钉消息失败", "send dingding message error"),
    VERIFY_CODE_EMPTY(540671, "验证码不能为空", "verification code must be filled."),
    VERIFY_FAIL(540672, "验证失败", "verification failed."),

    //权限
    GET_AUTH_FAILED(541701, "获取权限信息失败", "Fail to get auth info."),
    GET_USER_GROUP_FAILED(541702, "获取用户组信息失败", "Fail to get group info."),
    ADD_USER_GROUP_FAILED(541703, "加入用户组失败", "Fail to join group."),
    UPDATE_USER_GROUP_FAILED(541704, "更新用户组失败", "Fail to change group."),
    GET_USER_GROUP_RELATION_FAILED(541704, "获取用户用户组关系失败", "Get user group relation failed."),

    //企业
    GET_COMPANY_FAILED(541801, "获取企业信息失败", "Fail to get company info"),
    GET_USER_COMPANY_RELATION_FAILED(541802, "获取用户企业关联信息失败", "Fail to get user company relation"),
    ENTERPRISES_HAVE_BEEN_BLACKLIST(541803, "企业已在黑名单", "enterprises have been blacklisted"),
    QXB_FUZZY_ERROR(541804, "启信宝模糊查询错误", "qxb fuzzy query error"),
    QCC_FUZZY_ERROR(541805, "企查查模糊查询错误", "qcc fuzzy query error"),
    QUERY_COMPANY_ADMINISTRATOR_IS_NULL(541806, "企业管理员数据为空", "query company administrator is null"),
    COMPANY_HAS_BEEN_DISABLED(541807, "企业不存在或被禁用", "The company has been disabled"),
    OFFLINE_COMPANY_HAS_BEEN_DISABLED(541808, "线下企业不存在或被禁用", "The offline company has been disabled"),

    //平台
    PLATFORM_HAS_BEEN_DISABLED(541850, "平台不存在或被禁用", "The platform has been disabled"),



    //用户信息
    GET_USER_INFO_FAILED(541900, "获取用户信息失败", "Fail to get user info"),
    UPLOADING_PICTURE_FAILED(541901, "上传图片失败", "uploading picture failed"),
    THE_ACCOUNT_HAS_BEEN_BOUND(541403, "该账户已绑定", "the account has been bound"),
    THE_OPENID_HAS_BEEN_BOUND(541404, "openId已绑定", "the openId has been bound"),
    ROLE_DOES_NOT_EXIST(541405, "角色不存在", "role does not exist"),
    COMPANY_ROLE_DOES_NOT_EXIST(541405, "企业角色不存在", "company role does not exist"),
    ROLE_OF_ABNORMAL(541406, "角色异常", "role of abnormal"),
    USER_DOES_NOT_EXIST(541406, "用户不存在", "user does not exist"),
    PLATFORM_DOES_NOT_EXIST(541406, "平台标识不存在", "platform code does not exist"),
    USER_ROLE_DOES_NOT_EXIST(541407, "用户角色关联不存在", "The user role association does not exist"),
    USER_HAS_JOINED_THE_ENTERPRISE(541408, "该用户已经加入企业", "The user has joined the enterprise"),
    USER_IS_NOT_ADNMINSTRATOR(541409, "此用户不是管理员", "This user is not an administrator"),



    //平台

    ;





    //错误码
    private int code;

    //错误码提示中文
    private String descCn;

    //错误码提示英文
    private String desc;

    public int getCode() {
        return code;
    }


    public String getDescCn() {
        return descCn;
    }


    public String getDesc() {
        return desc;
    }



    ConstantStatusEnum(int code, String descCn, String desc) {
        this.code = code;
        this.descCn = descCn;
        this.desc = desc;
    }
}
