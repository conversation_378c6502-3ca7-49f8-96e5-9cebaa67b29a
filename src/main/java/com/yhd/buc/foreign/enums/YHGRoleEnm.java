package com.yhd.buc.foreign.enums;

/**
 * <AUTHOR>
 * @version Id:UserGroupEnm.java, v 0.12022/7/7 10:22 kangrong Exp $
 */
public enum YHGRoleEnm {
    PER_U("perU", "个人用户"),
    ORD_CO("ordCo", "普通企业"),
    ORD_CO_U("ordCoU", "普通企业用户"),
    ORD_MEM_CO("ordMemCo", "普通会员企业"),
    ORD_MEM_CO_U("ordMemCoU", "普通会员企业用户"),
    PREM_MEM_CO("premMemCo", "高级会员企业"),
    PREM_MEM_CO_U("premMemCoU", "高级会员企业用户"),
    SUPR_MEM_CO("suprMemCo", "至尊会员企业"),
    SUPR_MEM_CO_U("suprMemCoU", "至尊会员企业用户"),
    FROZE_SUPR_TO_ORD_MEM_CO_U("frozeSuprToOrdMemCoU", "至尊会员企业降级普通会员企业用户"),
    FROZE_SUPR_TO_PREM_MEM_CO_U("frozeSuprToPremMemCoU", "至尊会员企业降级高级会员企业用户"),
    FROZE_SUPR_TO_ORD_CO_U("frozeSuprToOrdCoU", "至尊会员企业降级普通企业用户"),
    FROZE_PREM_TO_ORD_CO_U("frozePremToOrdCoU", "高级会员企业降级普通企业用户"),
    FROZE_ORD_MEM_TO_ORD_CO_U("frozeOrdMemToOrdCoU", "普通会员企业降级普通企业用户"),
    MID_ORD_CO_U("midOrdCoU", "待审核普通企业用户"),
    UPG_ORD_MEM_CO_U("upgOrdMemCoU", "待升级会员企业用户（所在企业已从普通企业升级为普通会员企业，需要用户提交申请加入）"),
    UPG_PREM_MEM_CO_U("upgPremMemCoU", "待升级会员企业用户（所在企业已从普通企业升级为高级会员企业，需要用户提交申请加入）"),
    UPG_SUPR_MEM_CO_U("upgSuprMemCoU", "待升级会员企业用户（所在企业已从普通企业升级为至尊会员企业，需要用户提交申请加入）"),
    //（所在企业已从普通企业升级为普通会员企业，或高级会员企业，或至尊会员企业，需要用户提交申请加入)
    APPLY_MEM_CO_U("applyMemCoU", "申请中的企业用户或个人用户的中间角色")
    ;

    private final String code;
    private final String desc;

    YHGRoleEnm(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }}
