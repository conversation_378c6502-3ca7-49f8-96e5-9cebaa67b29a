package com.yhd.buc.foreign.enums;

/**
 * <AUTHOR>
 * @version Id:UserGroupEnm.java, v 0.12022/7/7 10:22 kangrong Exp $
 */
public enum CRMPlatformEnm {
    PER_U("010000", "YHD-dg","东莞"),
    ORD_CO("050000", "YHD-sz","苏州"),
    ORD_CO_U("040000", "YHD-yhg","怡惠购"),
    ORD_MEM_CO("070000", "YHD-fa","怡品"),
    ;

    private final String code;
    private final String desc;
    private final String descCn;

    CRMPlatformEnm(String code, String desc,String descCn) {
        this.code = code;
        this.descCn = descCn;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getDescCn() {
        return descCn;
    }

}
