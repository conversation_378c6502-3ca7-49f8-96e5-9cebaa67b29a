package com.yhd.buc.foreign.enums;

/**
 * <AUTHOR>
 * @version Id:UserGroupEnm.java, v 0.12022/7/7 10:22 kangrong Exp $
 */
public enum UserGroupEnm {

    MEMBER_ENTERPRISE_GROUP("memberEnterpriseGroup", "会员企业用户组"),

    PER_U_GROUP("perUGroup", "个人用户用户组"),
    ORD_CO_GROUP("ordCoGroup", "普通企业用户组"),
    ORD_CO_U_GROUP("ordCoUGroup", "普通企业用户用户组"),
    ORD_MEM_CO_GROUP("ordMemCoGroup", "普通会员企业用户组"),
    ORD_MEM_CO_U_GROUP("ordMemCoUGroup", "普通会员企业用户用户组"),
    PREM_MEM_CO_GROUP("premMemCoGroup", "高级会员企业用户组"),
    PREM_MEM_CO_U_GROUP("premMemCoUGroup", "高级会员企业用户用户组"),
    SUPR_MEM_CO_GROUP("suprMemCoGroup", "至尊会员企业用户组"),
    SUPR_MEM_CO_U_GROUP("suprMemCoUGroup", "至尊会员企业用户用户组"),
    FROZE_SUPR_TO_ORD_MEM_CO_GROUP("frozeSuprToOrdMemCoGroup", "至尊会员企业降级普通会员企业用户组"),
    FROZE_SUPR_TO_ORD_MEM_CO_U_GROUP("frozeSuprToOrdMemCoUGroup", "至尊会员企业降级普通会员企业用户用户组"),
    FROZE_SUPR_TO_PREM_MEM_CO_GROUP("frozeSuprToPremMemCoGroup", "至尊会员企业降级高级会员企业用户组"),
    FROZE_SUPR_TO_PREM_MEM_CO_U_GROUP("frozeSuprToPremMemCoUGroup", "至尊会员企业降级高级会员企业用户用户组"),
    FROZE_SUPR_TO_ORD_CO_GROUP("frozeSuprToOrdCoGroup", "至尊会员企业降级普通企业用户组"),
    FROZE_SUPR_TO_ORD_CO_U_GROUP("frozeSuprToOrdCoUGroup", "至尊会员企业降级普通企业用户用户组"),
    FROZE_PREM_TO_ORD_CO_GROUP("frozePremToOrdCoGroup", "高级会员企业降级普通企业用户组"),
    FROZE_PREM_TO_ORD_CO_U_GROUP("frozePremToOrdCoUGroup", "高级会员企业降级普通企业用户用户组"),
    FROZE_ORD_MEM_TO_ORD_CO_GROUP("frozeOrdMemToOrdCoGroup", "普通会员企业降级普通企业用户组"),
    FROZE_ORD_MEM_TO_ORD_CO_U_GROUP("frozeOrdMemToOrdCoUGroup", "普通会员企业降级普通企业用户用户组"),
    MID_ORD_CO_GROUP("midOrdCoGroup", "待审核普通企业用户组"),
    MID_ORD_CO_U_GROUP("midOrdCoUGroup", "待审核普通企业用户"),
    MID_ORD_MEM_CO_U_GROUP("midOrdMemCoUGroup", "个人加入普通会员用户组（待审核）"),
    MID_PREM_MEM_CO_U_GROUP("midPremMemCoUGroup", "个人加入高级会员用户组（待审核）"),
    MID_SUPR_MEM_CO_U_GROUP("midSuprMemCoUGroup", "个人加入至尊会员用户组（待审核"),
    //（所在企业已从普通企业升级为普通会员企业，或高级会员企业，或至尊会员企业，需要用户提交申请加入
    UPG_MEM_CO_U_GROUP("upgMemCoUGroup", "待升级会员企业用户用户组")
    ;

    private final String code;
    private final String desc;

    UserGroupEnm(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
