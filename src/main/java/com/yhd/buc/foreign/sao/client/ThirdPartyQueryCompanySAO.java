package com.yhd.buc.foreign.sao.client;

import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.Header;
import com.dtflys.forest.annotation.Var;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version Id:ThirdPartyQueryCompanySAO.java, v 0.12022/7/4 17:58 kangrong Exp $
 */
@Component
public interface ThirdPartyQueryCompanySAO {

    /**
     * 企查查模糊查询
     * */
    @Get(url = "{qccFuzzyQueryUrl}")
    String  qccFuzzyQuery(@Var("qccFuzzyQueryUrl")String qccFuzzyQueryUrl,@Header("Token")String token,@Header("Timespan")String timespan);


    /**
     * 企查查精确查询
     * */
    @Get(url = "{qccPreciseQueryUrl}")
    String  qccPreciseQuery(@Var("qccPreciseQueryUrl")String qccPreciseQueryUrl ,@Header("Token")String token,@Header("Timespan")String timespan);



    /**
     * 企信宝模糊查询
     * */
    @Get(url = "{qxbFuzzyQueryUrl}")
    String  qxbFuzzyQuery(@Var("qxbFuzzyQueryUrl")String qccFuzzyQueryUrl);


    /**
     * 企查查精确查询
     * */
    @Get(url = "{qxbPreciseQueryUrl}")
    String  qxbPreciseQuery(@Var("qxbPreciseQueryUrl")String qccPreciseQueryUrl);
}
