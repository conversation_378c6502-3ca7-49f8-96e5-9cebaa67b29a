package com.yhd.buc.foreign;

import com.yhd.feign.ann.EnableYHDFeignClients;
import com.yhd.lock.ann.EnableYHDLocks;
import com.yhd.lock.properties.RedisConnType;
import com.yhd.springdoc.EnableSpringDoc;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableAsync;


@EnableSpringDoc
@EnableYHDFeignClients
@EnableDiscoveryClient
@SpringBootApplication
@MapperScan(basePackages = "com.yhd.buc.foreign.dao")
@EnableAsync
@EnableYHDLocks(redisConnType = RedisConnType.CLUSTER)
public class SrvBucForeignApplication {

    public static void main(String[] args) {
        SpringApplication.run(SrvBucForeignApplication.class, args);
    }

}