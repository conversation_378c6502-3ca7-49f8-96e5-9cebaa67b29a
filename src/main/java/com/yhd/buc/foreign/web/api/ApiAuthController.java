package com.yhd.buc.foreign.web.api;

import com.yhd.buc.foreign.constant.RouteConstant;
import com.yhd.buc.foreign.pojo.vo.request.AuthRequestVO;
import com.yhd.buc.foreign.pojo.vo.response.RoleResponseVO;
import com.yhd.buc.foreign.service.api.RoleService;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.log.ann.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
/**
 * <AUTHOR>
 * @version Id:ApiController.java, v 0.12022/6/30 11:41 kangrong Exp $
 */
@RestController
@RequestMapping(value = RouteConstant.PREFIX + RouteConstant.VERSION + RouteConstant.APIS,produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "权限模块")
public class ApiAuthController {

    @Autowired
    private RoleService roleService;




    /**
     *查询用户角色信息
     */
    @PostMapping(RouteConstant.AUTH + RouteConstant.GET)
    @SysLog("查询用户角色信息")
    @Operation(summary = "查询用户角色信息")
    public BusinessResponse<RoleResponseVO> getRoleInfo(@RequestBody @Validated AuthRequestVO requestGetAuthVO) {
        return  roleService.getRoleInfo(requestGetAuthVO);
    }




}
