package com.yhd.buc.foreign.web.api;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.yhd.buc.foreign.constant.RouteConstant;
import com.yhd.buc.foreign.service.api.PlatformService;
import com.yhd.common.exception.BizException;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.log.ann.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Random;

/**
 * <AUTHOR>
 * @version Id:PlatformController.java, v 0.12023/4/18 17:14 kangrong Exp $
 */
@RestController
@RequestMapping(value = RouteConstant.PREFIX + RouteConstant.VERSION ,produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "平台模块")
public class TestController {

    private static final Logger logger = LogUtils.getLogger();


    @GetMapping("/test")
    public BusinessResponse<String> getTest(@RequestParam(value = "number")String number) throws InterruptedException {/*
        if (StringUtils.equals("1",number)){
            Thread.sleep(1100);
        }*/
        Random random = new Random();
/*        int randomNumber = random.nextInt(100) + 1;
        logger.info("randomNumber is :{}",randomNumber);
       if (randomNumber > 10){
           Thread.sleep(1100);
       }*/
       return BusinessResponse.ok("成功");
    }
}
