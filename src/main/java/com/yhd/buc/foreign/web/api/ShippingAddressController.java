package com.yhd.buc.foreign.web.api;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Stopwatch;
import com.yhd.buc.foreign.constant.RouteConstant;
import com.yhd.buc.foreign.pojo.vo.request.*;
import com.yhd.buc.foreign.pojo.vo.response.UserAddressResponseVO;
import com.yhd.buc.foreign.service.api.ShippingAddressService;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.log.ann.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version Id:AddressController.java, v 0.12022/6/21 9:30 kangrong Exp $
 */
@RestController
@RequestMapping(value = RouteConstant.PREFIX + RouteConstant.VERSION +RouteConstant.USER +RouteConstant.ADDRESS,produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "用户收货地址管理")
public class ShippingAddressController {

    private Logger logger = LogUtils.getLogger();
    @Autowired
    private ShippingAddressService shippingAddressService;
    
    /**
     *添加收货地址
     */
    @PostMapping(RouteConstant.ADD)
    @Operation(summary = "添加收货地址")
    @SysLog("添加收货地址")
    public BusinessResponse<String> createUserAddress(@RequestBody @Validated UserAddressRequestVO userAddressRequestVO) {
        logger.info("AddressController controller createAddress parameter :{}", userAddressRequestVO);
        Stopwatch stopwatch = Stopwatch.createStarted();
        BusinessResponse<String> businessResponse = shippingAddressService.addUserAddress(userAddressRequestVO);
        logger.info("AddressController controller createAddress cost : {} ms ,businessResponse is :{}", businessResponse, stopwatch.elapsed(TimeUnit.MILLISECONDS));
        return businessResponse;
    }

    /**
     *更新收货地址
     */
    @PutMapping(RouteConstant.UPDATE)
    @Operation(summary = "更新收货地址")
    @SysLog("更新收货地址")
    public BusinessResponse<String> updateUserAddress(@RequestBody @Validated UserAddressRequestVO userAddressRequestVO) {
        logger.info("AddressController controller updateUserAddress parameter :{}", userAddressRequestVO);
        Stopwatch stopwatch = Stopwatch.createStarted();
        BusinessResponse<String> businessResponse = shippingAddressService.updateUserAddress(userAddressRequestVO);
        logger.info("AddressController controller updateUserAddress cost : {} ms ,businessResponse is :{}", businessResponse, stopwatch.elapsed(TimeUnit.MILLISECONDS));
        return businessResponse;
    }

    /**
     *删除收货地址
     */
    @PutMapping( RouteConstant.DELETE)
    @Operation(summary = "删除收货地址")
    @SysLog("删除收货地址")
    public BusinessResponse<String> deleteUserAddress( @RequestBody @Validated UserAddressCodeRequestVO userAddressCodeRequestVO) {
        logger.info("AddressController controller deleteUserAddress parameter :{}", userAddressCodeRequestVO);
        Stopwatch stopwatch = Stopwatch.createStarted();
        BusinessResponse<String> businessResponse = shippingAddressService.deleteUserAddress(userAddressCodeRequestVO);
        logger.info("AddressController controller deleteUserAddress cost : {} ms ,businessResponse is :{}", businessResponse, stopwatch.elapsed(TimeUnit.MILLISECONDS));
        return businessResponse;
    }

    /**
     *查询收货地址集合
     */
    @PostMapping(RouteConstant.GET_LIST)
    @Operation(summary = "查询收货地址集合")
    @SysLog("查询收货地址集合")
    public BusinessResponse<PageInfo<UserAddressResponseVO>> getUserAddressList(@RequestBody @Validated UserCodePageRequestVO userCodePageRequestVO) {
        logger.info("AddressController controller getUserAddressList parameter :{}", userCodePageRequestVO);
        Stopwatch stopwatch = Stopwatch.createStarted();
        BusinessResponse< PageInfo<UserAddressResponseVO>> businessResponse = shippingAddressService.queryUserAddress(userCodePageRequestVO);
        logger.info("AddressController controller getUserAddressList cost : {} ms ,businessResponse is :{}", businessResponse, stopwatch.elapsed(TimeUnit.MILLISECONDS));
        return businessResponse;
    }

    /**
     *查询地址可用量
     */
    @PostMapping(RouteConstant.COUNT)
    @Operation(summary = "查询地址可用量")
    @SysLog("查询地址可用量")
    public BusinessResponse<Integer> getUserAddressCount( @RequestBody @Validated AddressTypeRequestVO addressTypeRequestVO) {
        logger.info("AddressController controller getUserAddressCount parameter :{}", addressTypeRequestVO);
        Stopwatch stopwatch = Stopwatch.createStarted();
        BusinessResponse<Integer> businessResponse = shippingAddressService.queryUserAddressNum(addressTypeRequestVO);
        logger.info("AddressController controller getUserAddressCount cost : {} ms ,businessResponse is :{}", businessResponse, stopwatch.elapsed(TimeUnit.MILLISECONDS));
        return businessResponse;
    }

    /**
     *设置默认地址
     */
    @PostMapping(RouteConstant.SET_DEFAULT)
    @Operation(summary = "设置默认地址")
    @SysLog("设置默认地址")
    public BusinessResponse<String> setUserAddressDefault(@RequestBody @Validated UserAddressCodeRequestVO userAddressCodeRequestVO) {
        logger.info("AddressController controller setUserAddressDefault parameter :{}", userAddressCodeRequestVO);
        Stopwatch stopwatch = Stopwatch.createStarted();
        BusinessResponse<String> businessResponse = shippingAddressService.setDefaultAddress(userAddressCodeRequestVO);
        logger.info("AddressController controller setUserAddressDefault cost : {} ms ,businessResponse is :{}", businessResponse, stopwatch.elapsed(TimeUnit.MILLISECONDS));
        return businessResponse;
    }


    /**
     *获取地址详情
     */
    @PostMapping(RouteConstant.DERAILS)
    @Operation(summary = "获取地址详情")
    @SysLog("获取地址详情")
    public BusinessResponse<UserAddressResponseVO> getUserAddressDerails(@RequestBody @Validated UserAddressCodeRequestVO userAddressCodeRequestVO) {
        logger.info("AddressController controller getUserAddressDerails parameter :{}", userAddressCodeRequestVO);
        Stopwatch stopwatch = Stopwatch.createStarted();
        BusinessResponse<UserAddressResponseVO> businessResponse = shippingAddressService.getUserAddressDerails(userAddressCodeRequestVO);
        logger.info("AddressController controller getUserAddressDerails cost : {} ms ,businessResponse is :{}", businessResponse, stopwatch.elapsed(TimeUnit.MILLISECONDS));
        return businessResponse;
    }

    /**
     *根据企业编码获取管理员收货地址集合
     *  @param companyCode 企业编码
     */
    @GetMapping(RouteConstant.GET + "/admin/address/list/by/companyCode")
    @SysLog("根据企业编码获取管理员集合")
    @Operation(summary = "根据企业编码获取管理员集合")
    public BusinessResponse<List<UserAddressResponseVO>> getCompanyAdminAddressList(@RequestParam(value = "companyCode",required = false) String companyCode) {
        return  shippingAddressService.getCompanyAdminAddressList(companyCode);
    }

}
