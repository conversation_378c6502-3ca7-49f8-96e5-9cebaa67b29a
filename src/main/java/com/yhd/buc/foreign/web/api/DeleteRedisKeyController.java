package com.yhd.buc.foreign.web.api;

import com.yhd.buc.foreign.constant.RouteConstant;
import com.yhd.buc.foreign.service.api.DeleteRedisKeyService;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.log.ann.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version Id:deleteRedisKeyControlller.java, v 0.12023/4/3 10:40 kangrong Exp $
 */
@RestController
@RequestMapping(value = RouteConstant.PREFIX + RouteConstant.VERSION + "/redis",produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "删除用户中心的键")
public class DeleteRedisKeyController {

    @Autowired
    private DeleteRedisKeyService deleteRedisKeyService;


    @GetMapping(value = RouteConstant.DELETE + RouteConstant.OFFLINE + RouteConstant.COMPANY)
    @SysLog("删除线下企业缓存")
    @Operation(summary = "删除线下企业缓存")
    public BusinessResponse<String> deleteOfflineCompanyKey(@RequestParam("erpCompanyCode") String erpCompanyCode, @RequestParam("subsidiaryCode") String subsidiaryCode) {
        return deleteRedisKeyService.deleteOfflineCompanyKey(erpCompanyCode,subsidiaryCode);
    }

    @SysLog("删除账号缓存")
    @Operation(summary = "删除账号缓存")
    @GetMapping(value = RouteConstant.DELETE + RouteConstant.USER)
    public BusinessResponse<String> logoutUser(@RequestParam(value = "account")String account) {
        return  deleteRedisKeyService.deleteUserInfo(account);
    }

}
