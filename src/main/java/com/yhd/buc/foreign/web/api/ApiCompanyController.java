package com.yhd.buc.foreign.web.api;

import com.yhd.buc.foreign.constant.RouteConstant;
import com.yhd.buc.foreign.pojo.vo.request.CompanyCodeListRequestVO;
import com.yhd.buc.foreign.pojo.vo.request.CompanyCodeRequestVO;
import com.yhd.buc.foreign.pojo.vo.request.ERPCompanyCodeListRequestVO;
import com.yhd.buc.foreign.pojo.vo.request.OfflineCompanyCodeListRequestVO;
import com.yhd.buc.foreign.pojo.vo.response.*;
import com.yhd.buc.foreign.service.api.CompanyService;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.log.ann.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id:ApiCompanyController.java, v 0.12022/7/27 10:06 kangrong Exp $
 */
@RestController
@RequestMapping(value = RouteConstant.PREFIX + RouteConstant.VERSION + RouteConstant.APIS,produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "企业模块")
public class ApiCompanyController {
    @Resource
    private CompanyService companyService;

    /**
     *获取线上企业信息
     */
    @PostMapping(RouteConstant.COMPANY + RouteConstant.GET)
    @SysLog("获取线上企业信息")
    @Operation(summary = "获取线上企业信息")
    public BusinessResponse<CompanyResponseVO> getCompanyInfo(@RequestBody  @Validated CompanyCodeRequestVO requestCompanyVO) {
        return  companyService.queryCompanyByCompanyCode(requestCompanyVO);
    }

    /**
     *批量获取线下企业信息
     */
    @PostMapping(RouteConstant.OFFLINE + RouteConstant.COMPANY + RouteConstant.GET_LIST)
    @SysLog("批量获取线下企业信息")
    @Operation(summary = "批量获取线下企业信息")
    public BusinessResponse<List<OfflineCompanyInfoResponseVO>> getOfflineCompanyInfoList(@RequestBody  @Validated CompanyCodeListRequestVO requestCompanyCodeListVO) {
        return companyService.queryOfflineCompanyListByCompanyCodeList(requestCompanyCodeListVO);
    }

    /**
     *根据ERP编码获取用户编码集合
     */
    @PostMapping(RouteConstant.COMPANY + "/get/admin/list/by/erpCompany")
    @SysLog("根据ERP编码获取用户编码集合")
    @Operation(summary = "根据ERP编码获取用户编码集合")
    public BusinessResponse<List<ApiUserAdminResponse>> getAdminListByErpCompany(@RequestBody ERPCompanyCodeListRequestVO erpCompanyCodeListRequestVO) {
        return companyService.getAdminListByErpCompany(erpCompanyCodeListRequestVO);
    }


    /**
     *根据erp企业编码集合批量获取线下企业信息
     */
    @PostMapping(RouteConstant.OFFLINE + RouteConstant.COMPANY + RouteConstant.GET_LIST + "/byErpCompanyList")
    @SysLog("根据erp企业编码集合批量获取线下企业信息(提供给开放平台)")
    @Operation(summary = "根据erp企业编码集合批量获取线下企业信息(提供给开放平台)")
    public BusinessResponse<OfflineCompanyInfoByErpCompanyResponseVO> getOfflineCompanyInfoListByErpCompanyList(@RequestBody  @Validated OfflineCompanyCodeListRequestVO offlineCompanyCodeListRequestVO) {
        return companyService.queryOfflineCompanyListByErpCompanyCodeList(offlineCompanyCodeListRequestVO);
    }
}
