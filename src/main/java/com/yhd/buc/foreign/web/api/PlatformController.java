package com.yhd.buc.foreign.web.api;

import com.yhd.buc.foreign.constant.RouteConstant;
import com.yhd.buc.foreign.service.api.PlatformService;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.log.ann.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id:PlatformController.java, v 0.12023/4/18 17:14 kangrong Exp $
 */
@RestController
@RequestMapping(value = RouteConstant.PREFIX + RouteConstant.VERSION + RouteConstant.PLATFORM,produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "平台模块")
public class PlatformController {

    @Autowired
    private PlatformService platformService;

    @PostMapping(RouteConstant.GET_LIST)
    @SysLog("获取平台集合")
    @Operation(summary = "获取平台集合")
    public BusinessResponse<List<Object>> getPlatform() {
        return BusinessResponse.ok(platformService.queryPlatformCodeList());
    }
}
