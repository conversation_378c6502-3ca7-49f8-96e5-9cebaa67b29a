package com.yhd.buc.foreign.web.api;
import com.github.pagehelper.PageInfo;
import com.yhd.buc.foreign.constant.RouteConstant;
import com.yhd.buc.foreign.pojo.vo.request.*;
import com.yhd.buc.foreign.pojo.vo.response.*;
import com.yhd.buc.foreign.service.api.UserService;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.log.ann.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id:ApiCompanyController.java, v 0.12022/7/27 10:06 kangrong Exp $
 */
@RestController
@RequestMapping(value = RouteConstant.PREFIX + RouteConstant.VERSION + RouteConstant.APIS,produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "用户模块")
public class ApiUserController {
    @Autowired
    private UserService userService;
    

    /**
     *查询用户基本信息(第三方调用)
     */
    @PostMapping(RouteConstant.USER  + RouteConstant.GET + RouteConstant.BASE_INFORMATION)
    @SysLog("查询用户基本信息(第三方调用)")
    @Operation(summary = "查询用户基本信息(第三方调用)")
    public BusinessResponse<UserBaseInfoResponseVO> getUserInfo(@RequestBody @Validated UserCodeRequestVO userCodeRequestVO) {
        return userService.getUserInfo(userCodeRequestVO);
    }

    /**
     *查询用户全部信息(第三方调用)
     */
    @PostMapping(RouteConstant.USER + RouteConstant.GET + RouteConstant.ALL_INFORMATION)
    @Operation(summary = "查询用户全部信息(第三方调用)")
    public BusinessResponse<ApiUserAllInfoResponseVO> getUserAllInformation(@RequestBody @Validated UserPlatformCodeRequestVO userPlatformCodeRequestVO) {
        return userService.getApiUserBackstageAllInfo(userPlatformCodeRequestVO);
    }

    /**
     *查询用户全部信息(前端调用)
     */
    @PostMapping(RouteConstant.FRONT + RouteConstant.USER + RouteConstant.GET + RouteConstant.ALL_INFORMATION)
    @Operation(summary = "查询用户全部信息(用户中心前台调用)")
    public BusinessResponse<ApiUserFrontInfoResponseVO> getUserFrontInformation(@RequestBody @Validated UserPlatformCodeRequestVO userPlatformCodeRequestVO) {
        return userService.getApiFrontInfo(userPlatformCodeRequestVO);
    }

    /**
     *查询用户全部信息集合(前端调用)多个平台
     */
    @PostMapping(RouteConstant.FRONT + RouteConstant.USER + RouteConstant.GET_LIST + RouteConstant.ALL_INFORMATION+"/by_platform_list")
    @Operation(summary = "查询用户全部信息(用户中心前台调用)")
    public BusinessResponse<List<ApiUserFrontInfoResponseVO>> getUserFrontInformationListByPlatformList(@RequestBody @Validated List<UserPlatformCodeRequestVO> userPlatformCodeRequestVO) {
        List<ApiUserFrontInfoResponseVO> apiUserFrontInfoResponseVOS = new ArrayList<>();
        userPlatformCodeRequestVO.forEach(e ->{
            BusinessResponse<ApiUserFrontInfoResponseVO> apiFrontInfo =  userService.getApiFrontInfo(e);
            if (apiFrontInfo.success() && ObjectUtils.isNotEmpty(apiFrontInfo.getData())){
                apiUserFrontInfoResponseVOS.add(apiFrontInfo.getData());
            }
        });
        return BusinessResponse.ok(apiUserFrontInfoResponseVOS);
    }

    /**
     *查询用户全部信息集合(前端调用)(同一个平台)
     */
    @PostMapping(RouteConstant.FRONT + RouteConstant.USER + RouteConstant.GET_LIST + RouteConstant.ALL_INFORMATION)
    @SysLog("查询用户全部信息集合(前端调用)")
    @Operation(summary = "查询用户全部信息集合(前端调用)")
    public BusinessResponse<List<ApiUserFrontInfoResponseVO>> getUserFrontInformationList(@RequestBody @Validated UserCodeListPlatformRequestVO userCodeListPlatformRequestVO) {
        return userService.getApiFrontInfoList(userCodeListPlatformRequestVO);
    }


    /**
     *根据企业编码获取旗下用户全部信息集合(第三方调用)
     */
    @PostMapping(RouteConstant.GET + RouteConstant.USER_LIST  + RouteConstant.BY_COMPANY_CODE)
    @SysLog("根据企业编码获取旗下用户全部信息集合(第三方调用)")
    @Operation(summary = "根据企业编码获取旗下用户全部信息集合(第三方调用)")
    public BusinessResponse<List<ApiUserAllInfoResponseVO>> getUserInfoListByCompanyCode( @RequestBody @Validated CompanyCodePlatformRequestVO companyCodePlatformRequestVO) {
        return userService.getUserInfoListByCompanyCode(companyCodePlatformRequestVO);
    }

    /**
     *根据企业编码获取旗下用户信息集合(分页)（前台）
     */
    @PostMapping(RouteConstant.GET + RouteConstant.USER_LIST_PAGE   + RouteConstant.BY_COMPANY_CODE)
    @SysLog("根据企业编码获取旗下用户信息集合(分页)（前台）")
    @Operation(summary = "根据企业编码获取旗下用户信息集合(分页)（前台）")
    public BusinessResponse<PageInfo<UserAdminListResponseVO>> getUserInfoListPageByCompanyCode(@RequestBody @Validated CompanyPageRequestVO companyPageRequestVO) {
        return  userService.getUserInfoListPageByCompanyCode(companyPageRequestVO);
    }


    /**
     *批量获取用户基础信息
     */
    @PostMapping(RouteConstant.GET + RouteConstant.BASE_INFORMATION +  RouteConstant.USER_LIST)
    @SysLog("批量获取用户信息(第三方调用)")
    @Operation(summary = "批量获取用户信息(第三方调用)")
    public BusinessResponse<List<UserBaseInfoResponseVO>> getUserBaseInfoList( @RequestBody @Validated UserCodeListRequestVO userCodeListRequestVO) {
        return userService.getUserInfoList(userCodeListRequestVO);
    }


    /**
     *批量获取用户全部信息(第三方调用)
     */
    @PostMapping(RouteConstant.GET + RouteConstant.ALL_INFORMATION +  RouteConstant.USER_LIST)
    @SysLog("批量获取用户全部信息(第三方调用)")
    @Operation(summary = "批量获取用户全部信息(第三方调用)")
    public BusinessResponse<List<ApiUserAllInfoResponseVO>> getAllUserInfoList( @RequestBody @Validated UserCodeListPlatformRequestVO userCodeListPlatformRequestVO) {
        return userService.getUserAllInfoList(userCodeListPlatformRequestVO);
    }

    /**
     *更新用户头像
     */
    @PostMapping(RouteConstant.USER +RouteConstant.HEAD_PORTRAIT + RouteConstant.UPDATE)
    @SysLog("更新用户头像(第三方调用)")
    @Operation(summary = "更新用户头像(第三方调用)")
    public BusinessResponse<String> updateUserHeadPortrait( @RequestBody @Validated UserHeadPortraitRequestVO userHeadPortraitRequestVO) {
        return userService.updateUserHeadPortrait(userHeadPortraitRequestVO);
    }

    /**
     *更新用户基础信息
     */
    @PostMapping(RouteConstant.USER + RouteConstant.UPDATE)
    @SysLog("更新用户基础信息(第三方调用)")
    @Operation(summary = "更新用户基础信息(第三方调用)")
    public BusinessResponse<String> updateUserInfo( @RequestBody @Validated UserBaseInfoRequestVO userBaseInfoRequestVO) {
        return userService.updateUserInfo(userBaseInfoRequestVO);
    }


    /**
     *查询用户全部信息以及线下企业信息(第三方调用)
     */
    @PostMapping(RouteConstant.GET + RouteConstant.ALL_INFORMATION + RouteConstant.OFFLINE + RouteConstant.COMPANY)
    @SysLog("查询用户全部信息以及线下企业信息(第三方调用)")
    @Operation(summary = "查询用户全部信息以及线下企业信息(第三方调用)")
    public BusinessResponse<ApiUserAllInfoAndOfflineCompanyResponseVO> getUserAllInformationAndOfflineCompany(@RequestBody @Validated UserPlatformCodeRequestVO userPlatformCodeRequestVO) {
        return userService.getUserAllInformationAndOfflineCompany(userPlatformCodeRequestVO);
    }


    /**
     *根据企业编码获取旗下已经加入企业的用户信息集合(分页)
     */
    @PostMapping(RouteConstant.GET +   "/join/company" + RouteConstant.USER_LIST  + RouteConstant.BY_COMPANY_CODE)
    @SysLog("根据企业编码获取旗下已经加入企业的用户信息集合(分页)")
    @Operation(summary = "根据企业编码获取旗下已经加入企业的用户信息集合(分页)")
    public BusinessResponse<PageInfo<JoinCompanyUseListResponseVO>> getJoinCompanyUserInfoListPageByCompanyCode( @RequestBody @Validated CompanyPageRequestVO companyPageRequestVO) {
        return userService.getJoinCompanyUserInfoListPageByCompanyCode(companyPageRequestVO);
    }


    @SysLog("根据用户名称查询用户列表,至多返回200条")
    @Operation(summary = "根据用户名称查询用户列表,至多返回200条")
    @GetMapping(value = "/get/user/by/username")
    public BusinessResponse<List<UserInfoByUserNameResponseVO>> getUserByUsername(@RequestParam(value = "userName")String userName) {
        return userService.getUserByUsername(userName);
    }

    @SysLog("根据平台获取登录过的用户编码")
    @Operation(summary = "根据平台获取登录过的用户编码")
    @PostMapping(value = "/get/user_code/list/by/platform_code")
    public BusinessResponse<List<String>> getUserCodeListByPlatformCode(@RequestBody @Validated PlatformCodePageRequest platformCodePageRequest) {
        return userService.getUserCodeListByPlatformCode(platformCodePageRequest);
    }

}
