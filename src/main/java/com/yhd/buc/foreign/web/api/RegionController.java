package com.yhd.buc.foreign.web.api;

import com.yhd.buc.foreign.constant.RouteConstant;
import com.yhd.buc.foreign.pojo.vo.request.RegionRequestVO;
import com.yhd.buc.foreign.pojo.vo.response.SyncRegionResponseVO;
import com.yhd.buc.foreign.service.api.PlatformService;
import com.yhd.buc.foreign.service.api.SyncRegionService;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.log.ann.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id:PlatformController.java, v 0.12023/4/18 17:14 kangrong Exp $
 */
@RestController
@RequestMapping(value = RouteConstant.PREFIX + RouteConstant.VERSION + RouteConstant.REGION,produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "地区管理")
public class RegionController {

    @Autowired
    private SyncRegionService syncRegionService;

    @PostMapping(RouteConstant.GET_LIST)
    @SysLog("获取地区集合")
    @Operation(summary = "获取地区集合")
    public BusinessResponse<List<SyncRegionResponseVO>> getRegionRequestVO(@RequestBody RegionRequestVO regionRequestVO) {
        return syncRegionService.getRegionList(regionRequestVO);
    }
}
