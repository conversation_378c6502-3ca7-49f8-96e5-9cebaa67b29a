package com.yhd.buc.foreign.service.impl;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yhd.buc.foreign.constant.CommonConstant;
import com.yhd.buc.foreign.constant.RedisConstant;
import com.yhd.buc.foreign.dao.PlatformDao;
import com.yhd.buc.foreign.pojo.po.PlatformPO;
import com.yhd.buc.foreign.service.api.PlatformService;
import com.yhd.common.util.LogUtils;
import com.yhd.redis.service.RedisService;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id:PlatformService.java, v 0.12023/3/21 15:23 kangrong Exp $
 */
@Service
public class PlatformServiceImpl implements PlatformService {

    private static final Logger logger = LogUtils.getLogger();

    @Resource
    private RedisService redisService;

    @Resource
    private PlatformDao platformDao;
    /**
     * 获取平台编码集合
     */
    @Override
    public List<Object> queryPlatformCodeList() {
        logger.info("visit PlatformService queryPlatformCodeList");
        List<Object> platformCodeList;
         platformCodeList = redisService.get(RedisConstant.YHD_SERVICE_BUC_PLATFORM_CODE_LIST);
        if (CollectionUtils.isEmpty(platformCodeList)){
            LambdaQueryWrapper<PlatformPO> platformPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            platformPOLambdaQueryWrapper.select(PlatformPO::getPlatformCode).eq(PlatformPO::getStatus, CommonConstant.ENABLED);
            platformCodeList = platformDao.selectObjs(platformPOLambdaQueryWrapper);
            logger.info("platformDao selectObjs result is :{}",platformCodeList);
            Boolean result = redisService.set(RedisConstant.YHD_SERVICE_BUC_PLATFORM_CODE_LIST, platformCodeList);
            logger.info("redisService set key is :{} value is :{}",RedisConstant.YHD_SERVICE_BUC_PLATFORM_CODE_LIST,result);
        }
        return platformCodeList;
    }
}
