package com.yhd.buc.foreign.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yhd.buc.foreign.constant.CommonConstant;
import com.yhd.buc.foreign.dao.ComHomeDao;
import com.yhd.buc.foreign.enums.ConstantStatusEnum;
import com.yhd.buc.foreign.pojo.po.ComHomePO;
import com.yhd.buc.foreign.pojo.vo.request.*;
import com.yhd.buc.foreign.pojo.vo.response.ComHomeDetailsResponseVO;
import com.yhd.buc.foreign.service.api.ComHomeService;
import com.yhd.common.exception.BizException;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id:ShippingAddressServiceImpl.java, v 0.12023/3/9 15:22 kangrong Exp $
 */
@Service
public class ComHomeServiceImpl implements ComHomeService {

    private static final Logger logger = LogUtils.getLogger();

    @Resource
    private ComHomeDao comHomeDao;


    //上传系统/模块名称
    @Value("${oss.cmsUrl}")
    private String cmsUrl;


    /**
     * 获取平台主页样式配置详情
     *
     * @param platformCodeRequest
     */
    @Override
    public BusinessResponse<ComHomeDetailsResponseVO> getComHomeDetails(PlatformCodeRequest platformCodeRequest) {
        logger.info("visit ComHomeService getComHomeDetails");
        LambdaQueryWrapper<ComHomePO> queryWrapper = new LambdaQueryWrapper<ComHomePO>()
                .eq(ComHomePO::getPlatformCode, platformCodeRequest.getPlatformCode())
                .eq(ComHomePO::getStatus, CommonConstant.ENABLED);
        ComHomePO comHomePO = comHomeDao.selectOne(queryWrapper);
        logger.info("comHomeDao selectOne result is :{}",comHomePO);
        if (ObjectUtils.isEmpty(comHomePO)){
            throw new BizException(ConstantStatusEnum.DATABASE_QUERY_RESULT_IS_NULL.getCode(), ConstantStatusEnum.DATABASE_QUERY_RESULT_IS_NULL.getDesc());
        }
        ComHomeDetailsResponseVO comHomeDetailsResponseVO = new ComHomeDetailsResponseVO();
        BeanUtils.copyProperties(comHomePO,comHomeDetailsResponseVO);
        comHomeDetailsResponseVO.setLoginRegistrationPicture(cmsUrl + comHomeDetailsResponseVO.getLoginRegistrationPicture());
        //加前缀
        comHomeDetailsResponseVO.setLogoPicture(cmsUrl + comHomeDetailsResponseVO.getLogoPicture());

        comHomeDetailsResponseVO.setLogoUrl(cmsUrl + comHomeDetailsResponseVO.getLogoPicture());
        String textDocumentUrl = comHomePO.getTextDocumentUrl();
        List<LinkRequestVO> textDocumentList = JSONUtil.toList(textDocumentUrl, LinkRequestVO.class);
        textDocumentList.forEach(e ->
                e.setLinkUrl(cmsUrl + e.getLinkUrl())
        );
        //配置链接
        if (ObjectUtils.isNotEmpty(comHomePO.getFriendshipLink())){
            List<LinkRequestVO> friendshipList = JSONUtil.toList(comHomePO.getFriendshipLink(), LinkRequestVO.class);
            comHomeDetailsResponseVO.setFriendshipList(friendshipList);
        }
        if (ObjectUtils.isNotEmpty(comHomePO.getBottomLink())){
            List<LinkRequestVO> bottomLinkList = JSONUtil.toList(comHomePO.getBottomLink(), LinkRequestVO.class);
            comHomeDetailsResponseVO.setBottomLinkList(bottomLinkList);
        }
        return BusinessResponse.ok(comHomeDetailsResponseVO);
    }
}
