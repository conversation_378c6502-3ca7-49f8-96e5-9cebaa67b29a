package com.yhd.buc.foreign.service.api;

import com.yhd.buc.foreign.pojo.po.CompanyPO;
import com.yhd.buc.foreign.pojo.vo.request.CompanyCodeListRequestVO;
import com.yhd.buc.foreign.pojo.vo.request.CompanyCodeRequestVO;
import com.yhd.buc.foreign.pojo.vo.request.ERPCompanyCodeListRequestVO;
import com.yhd.buc.foreign.pojo.vo.request.OfflineCompanyCodeListRequestVO;
import com.yhd.buc.foreign.pojo.vo.response.ApiUserAdminResponse;
import com.yhd.buc.foreign.pojo.vo.response.CompanyResponseVO;
import com.yhd.buc.foreign.pojo.vo.response.OfflineCompanyInfoByErpCompanyResponseVO;
import com.yhd.buc.foreign.pojo.vo.response.OfflineCompanyInfoResponseVO;
import com.yhd.common.pojo.vo.BusinessResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id:CompanyServiceImpl.java, v 0.12022/7/2 10:31 kangrong Exp $
 */
public interface CompanyService {


    /**
     * 根据用户编码返回关联的企业
     * */
    CompanyPO queryCompanyByUserCode(String userCode);

    /**
     * 查询线上企业信息
     * */
    BusinessResponse<CompanyResponseVO> queryCompanyByCompanyCode(CompanyCodeRequestVO requestCompanyVO);


    /**
     * 批量查询线下企业信息
     * */
    BusinessResponse<List<OfflineCompanyInfoResponseVO>> queryOfflineCompanyListByCompanyCodeList(CompanyCodeListRequestVO requestCompanyCodeListVO);

    /**
     * 查询线下企业信息
     * */
    OfflineCompanyInfoResponseVO queryOfflineCompany(String companyCode,String platformCode);

    /**
     * 根据erp编码查询管理员列表
     * */
    BusinessResponse<List<ApiUserAdminResponse>> getAdminListByErpCompany(ERPCompanyCodeListRequestVO erpCompanyCodeListRequestVO);

    /**
     * 根据erp编码查询线下企业信息集合
     * */
    BusinessResponse<OfflineCompanyInfoByErpCompanyResponseVO> queryOfflineCompanyListByErpCompanyCodeList(OfflineCompanyCodeListRequestVO offlineCompanyCodeListRequestVO);
}
