package com.yhd.buc.foreign.service.api;

import com.yhd.buc.foreign.pojo.vo.request.ThirdQueryCompanyRequestVO;
import com.yhd.common.pojo.vo.BusinessResponse;

/**
 * <AUTHOR>
 * @version Id:ThirdPlatformService.java, v 0.12022/7/4 15:19 kangrong Exp $
 */
public interface ThirdPlatformService {
    /**
     * 企查查企业查询接口
     * @param thirdQueryCompanyRequestVO 企查查接收VO
     * */
    BusinessResponse<Object> thirdPartyQueryCompany(ThirdQueryCompanyRequestVO thirdQueryCompanyRequestVO);
}
