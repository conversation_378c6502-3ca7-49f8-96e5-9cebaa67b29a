package com.yhd.buc.foreign.service.api;
import com.github.pagehelper.PageInfo;
import com.yhd.buc.foreign.pojo.vo.request.*;
import com.yhd.buc.foreign.pojo.vo.response.*;
import com.yhd.common.pojo.vo.BusinessResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id:UserService.java, v 0.12022/6/30 16:11 kangrong Exp $
 */
public interface UserService {
    /**
     * 获取用户所有信息
     * */
    BusinessResponse<ApiUserAllInfoResponseVO> getApiUserBackstageAllInfo(UserPlatformCodeRequestVO userPlatformCodeRequestVO);


    /**
     * 获取用户基础信息
     * */
    BusinessResponse<UserBaseInfoResponseVO> getUserInfo(UserCodeRequestVO userCodeRequestVO);


    /**
     * 获取前台用户信息
     * */
    BusinessResponse<ApiUserFrontInfoResponseVO> getApiFrontInfo(UserPlatformCodeRequestVO userPlatformCodeRequestVO);


    /**
     * 根据企业编码获取旗下用户信息集合
     * */
    BusinessResponse<List<ApiUserAllInfoResponseVO>> getUserInfoListByCompanyCode(CompanyCodePlatformRequestVO companyCodePlatformRequestVO);

    /**
     * 根据企业编码获取旗下用户信息分页集合
     * */
    BusinessResponse<PageInfo<UserAdminListResponseVO>> getUserInfoListPageByCompanyCode(CompanyPageRequestVO companyPageRequestVO);


    /**
     * 批量获取基础用户信息
     * */
    BusinessResponse<List<UserBaseInfoResponseVO>> getUserInfoList(UserCodeListRequestVO userCodeListRequestVO);

    /**
     * 更新用户基础信息
     * */
    BusinessResponse<String> updateUserInfo(UserBaseInfoRequestVO userBaseInfoRequestVO);

    /**
     * 更新头像
     * */
    BusinessResponse<String> updateUserHeadPortrait(UserHeadPortraitRequestVO userHeadPortraitRequestVO);


    /**
     * 批量获取全部用户信息
     * */
    BusinessResponse<List<ApiUserAllInfoResponseVO>> getUserAllInfoList(UserCodeListPlatformRequestVO userCodeListPlatformRequestVO);

    /**
     * 获取前台用户信息集合
     * */
    BusinessResponse<List<ApiUserFrontInfoResponseVO>> getApiFrontInfoList(UserCodeListPlatformRequestVO userCodeListPlatformRequestVO);

    /**
     *查询用户全部信息以及线下企业信息(第三方调用)
     */
    BusinessResponse<ApiUserAllInfoAndOfflineCompanyResponseVO> getUserAllInformationAndOfflineCompany(UserPlatformCodeRequestVO userPlatformCodeRequestVO);

    /**
     * 根据企业编码获取已加入旗下用户信息集合(分页)
     * */
    BusinessResponse<PageInfo<JoinCompanyUseListResponseVO>> getJoinCompanyUserInfoListPageByCompanyCode( CompanyPageRequestVO companyPageRequestVO);

    /**
     * 根据用户名称查询用户列表,至多返回200条
     * */
    BusinessResponse<List<UserInfoByUserNameResponseVO>> getUserByUsername(String userName);

    /**
     * 根据平台获取登录过的用户编码
     * */
    BusinessResponse<List<String>> getUserCodeListByPlatformCode(PlatformCodePageRequest platformCodePageRequest);
}
