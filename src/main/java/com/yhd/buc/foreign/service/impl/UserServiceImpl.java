package com.yhd.buc.foreign.service.impl;


import cn.hutool.core.bean.BeanUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yhd.buc.foreign.constant.CommonConstant;
import com.yhd.buc.foreign.constant.RedisConstant;
import com.yhd.buc.foreign.dao.*;
import com.yhd.buc.foreign.enums.ConstantStatusEnum;
import com.yhd.buc.foreign.pojo.po.*;
import com.yhd.buc.foreign.pojo.vo.request.*;
import com.yhd.buc.foreign.pojo.vo.response.*;
import com.yhd.buc.foreign.service.api.*;
import com.yhd.buc.foreign.utils.PageUtil;
import com.yhd.buc.foreign.utils.RedisUserUtils;
import com.yhd.buc.foreign.utils.SqlFormatUtil;
import com.yhd.common.exception.BizException;
import com.yhd.common.json.JSONUtil;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.context.UserInfoContext;
import com.yhd.redis.service.RedisService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id:UserService.java, v 0.12022/6/30 16:11 kangrong Exp $
 */
@Service
public class UserServiceImpl implements UserService {

    private static final Logger logger = LogUtils.getLogger();

    @Resource
    private RedisService redisService;

    @Resource
    private UserInfoDAO userInfoDAO;

    @Resource
    private PlatformRelationUserDAO platformRelationUserDAO;
    @Resource
    private AccountDAO accountDAO;

    @Resource
    private CompanyService companyService;

    @Resource
    private RoleService roleService;

    @Resource
    private RoleRelationCompanyDAO roleRelationCompanyDAO;

    @Resource
    private RoleDAO roleDAO;

    @Resource
    private ResourcesService resourcesService;


    @Resource
    private ResourcesClosedDimensionService resourcesClosedDimensionService;


    @Resource
    private AccountQqDAO accountQqDAO;

    @Resource
    private CompanyReviewDao companyReviewDao;

    @Resource
    private AdminReviewRecordDAO adminReviewRecordDAO;

    @Resource
    private WechatOfficialDAO wechatOfficialDAO;

    @Resource
    private CompanyRelationUserDAO companyRelationUserDAO;


    @Resource
    private CompanyAdministratorDao companyAdministratorDao;


    @Resource
    private AccountWechatDAO accountWechatDAO;


    @Resource
    private PlatformService platformService;

    @Resource
    private RedisUserUtils redisUserUtils;

    //上传系统/模块名称
    @Value("${oss.frontUrl}")
    private String fontUrl;


    /**
     * 获取用户所有信息
     *
     * @param userPlatformCodeRequestVO
     */
    @Override
    public BusinessResponse<ApiUserAllInfoResponseVO> getApiUserBackstageAllInfo(UserPlatformCodeRequestVO userPlatformCodeRequestVO) {
        logger.info("visit UserService getApiUserBackstageAllInfo");
         String platformCode = userPlatformCodeRequestVO.getPlatformCode();
        List<Object> list = platformService.queryPlatformCodeList();
        if (!list.contains(platformCode)){
            throw new BizException(ConstantStatusEnum.PLATFORM_HAS_BEEN_DISABLED.getCode(), ConstantStatusEnum.PLATFORM_HAS_BEEN_DISABLED.getDescCn());
        }
        //获取用户全部信息
        RedisUserAllInfoResponseVO apiUserAllInfo = getApiUserAllInfo(userPlatformCodeRequestVO);
        ApiUserAllInfoResponseVO apiUserAllInfoResponseVO = new ApiUserAllInfoResponseVO();
        BeanUtils.copyProperties(apiUserAllInfo,apiUserAllInfoResponseVO);
        return BusinessResponse.ok(apiUserAllInfoResponseVO);
    }

    /**
     * 获取用户基础信息
     *
     * @param userCodeRequestVO
     */
    @Override
    public BusinessResponse<UserBaseInfoResponseVO> getUserInfo(UserCodeRequestVO userCodeRequestVO) {
        logger.info("visit UserService getUserInfo");
        String userCode = userCodeRequestVO.getUserCode();
        UserBaseInfoResponseVO responseUserInfo = new UserBaseInfoResponseVO();

        //查缓存
        LambdaQueryWrapper<UserInfoPO> userInfoPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (!redisService.hHasKey(RedisConstant.YHD_SERVICE_BUC_USER_BASE_INFO,userCode)){
            userInfoPOLambdaQueryWrapper.eq(UserInfoPO::getUserCode,userCode).eq(UserInfoPO::getStatus, CommonConstant.ENABLED);
            UserInfoPO userInfoPO = userInfoDAO.selectOne(userInfoPOLambdaQueryWrapper);
            logger.info("userInfoDAO selectOne result is :{}",userInfoPO);
            if (ObjectUtils.isEmpty(userInfoPO)){
                throw new BizException(ConstantStatusEnum.USER_DOES_NOT_EXIST.getCode(), ConstantStatusEnum.USER_DOES_NOT_EXIST.getDescCn());
            }
            BeanUtils.copyProperties(userInfoPO,responseUserInfo);
            responseUserInfo.setTestUser(userInfoPO.getIsTest());
            if (StringUtils.isNotBlank(userInfoPO.getAvatarPath())){
                responseUserInfo.setAvatarPath(fontUrl + userInfoPO.getAvatarPath());
            }
            responseUserInfo.setUserName(userInfoPO.getUsername());

            //判定绑定
            setThirdParty(userCode, responseUserInfo);

            //设置用户企业状态
            setCompanyStatus(userCode,responseUserInfo);

            responseUserInfo.setRegisterDate(userInfoPO.getCreatedDate());

            //判断是否是管理员

            //缓存到Redis
            String responseUserInfoJson = JSONUtil.toJson(responseUserInfo);
            logger.info("get responseUserInfoVOJson is :{}",responseUserInfoJson);
            Boolean result = redisService.hset(RedisConstant.YHD_SERVICE_BUC_USER_BASE_INFO,userCode,responseUserInfoJson);
            logger.info("redis set userInfoMap result :{}",result);
        }else {
            responseUserInfo =  JSONUtil.fromObject(redisService.hget(RedisConstant.YHD_SERVICE_BUC_USER_BASE_INFO, userCode).toString(),UserBaseInfoResponseVO.class);
        }
        return BusinessResponse.ok(responseUserInfo);
    }

    /**
     * 获取前台用户信息
     *
     * @param userPlatformCodeRequestVO
     */
    @Override
    public BusinessResponse<ApiUserFrontInfoResponseVO> getApiFrontInfo(UserPlatformCodeRequestVO userPlatformCodeRequestVO) {
        logger.info("visit ApiService  getApiFrontInfo");
        RedisUserAllInfoResponseVO apiUserAllInfo = getApiUserAllInfo(userPlatformCodeRequestVO);
        ApiUserFrontInfoResponseVO apiUserFrontInfoResponseVO = new ApiUserFrontInfoResponseVO();
        BeanUtils.copyProperties(apiUserAllInfo,apiUserFrontInfoResponseVO);
        //获取专属跟单
        if (StringUtils.isNotBlank(apiUserFrontInfoResponseVO.getCompanyCode())){
            OfflineCompanyInfoResponseVO offlineCompanyInfoResponseVO = companyService.queryOfflineCompany(apiUserFrontInfoResponseVO.getCompanyCode(), apiUserFrontInfoResponseVO.getPlatformCode());
            if (!ObjectUtils.isEmpty(offlineCompanyInfoResponseVO)){
                apiUserFrontInfoResponseVO.setMerchandiserJobNumber(offlineCompanyInfoResponseVO.getMerchandiserJobNumber());
                apiUserFrontInfoResponseVO.setMerchandiserName(offlineCompanyInfoResponseVO.getMerchandiserName());
                apiUserFrontInfoResponseVO.setMerchandiserMobile(offlineCompanyInfoResponseVO.getMerchandiserMobile());
                apiUserFrontInfoResponseVO.setMerchandiserEmail(offlineCompanyInfoResponseVO.getMerchandiserEmail());
                apiUserFrontInfoResponseVO.setMerchandiserQq(offlineCompanyInfoResponseVO.getMerchandiserQq());
                apiUserFrontInfoResponseVO.setMerchandiserEnterpriseWechatCodeUrl(offlineCompanyInfoResponseVO.getMerchandiserEnterpriseWechatCodeUrl());
                apiUserFrontInfoResponseVO.setOwnershipCompany(offlineCompanyInfoResponseVO.getOwnershipCompany());
                apiUserFrontInfoResponseVO.setOperatorEmail(offlineCompanyInfoResponseVO.getOperatorEmail());
                apiUserFrontInfoResponseVO.setOperatorJobNumber(offlineCompanyInfoResponseVO.getOperatorJobNumber());
                apiUserFrontInfoResponseVO.setOperatorMobile(offlineCompanyInfoResponseVO.getOperatorMobile());
                apiUserFrontInfoResponseVO.setOperatorName(offlineCompanyInfoResponseVO.getOperatorName());
                apiUserFrontInfoResponseVO.setOperatorQq(offlineCompanyInfoResponseVO.getOperatorQq());
                apiUserFrontInfoResponseVO.setOperatorPost(offlineCompanyInfoResponseVO.getOperatorPost());
                apiUserFrontInfoResponseVO.setMerchandiserPost(offlineCompanyInfoResponseVO.getMerchandiserPost());
                apiUserFrontInfoResponseVO.setDiscountLevel(offlineCompanyInfoResponseVO.getDiscountLevel());
                apiUserFrontInfoResponseVO.setOperatorEnterpriseWechatCodeUrl(offlineCompanyInfoResponseVO.getOperatorEnterpriseWechatCodeUrl());
            }
        }
        return BusinessResponse.ok(apiUserFrontInfoResponseVO);
    }

    /**
     * 根据企业编码获取旗下用户信息集合
     *
     * @param companyCodePlatformRequestVO
     */
    @Override
    public BusinessResponse<List<ApiUserAllInfoResponseVO>> getUserInfoListByCompanyCode(CompanyCodePlatformRequestVO companyCodePlatformRequestVO) {
        logger.info("visit UserService getUserInfoListByCompanyCode");
        List<ApiUserAllInfoResponseVO> userBaseInfoResponseVOList = new ArrayList<>();
        LambdaQueryWrapper<CompanyRelationUserPO> companyRelationUserPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        companyRelationUserPOLambdaQueryWrapper.eq(CompanyRelationUserPO::getCompanyCode,companyCodePlatformRequestVO.getCompanyCode())
                .eq(CompanyRelationUserPO::getStatus,CommonConstant.ENABLED);
        List<CompanyRelationUserPO> companyRelationUserPOList = companyRelationUserDAO.selectList(companyRelationUserPOLambdaQueryWrapper);
        logger.info("getUserInfoListByCompanyCode companyRelationUserDAO selectList size is :{}",companyRelationUserPOList.size());
        if (!companyRelationUserPOList.isEmpty()){
            UserCodeListPlatformRequestVO userCodeListPlatformRequestVO = new UserCodeListPlatformRequestVO();
            userCodeListPlatformRequestVO.setPlatformCode(companyCodePlatformRequestVO.getPlatformCode());
            List<String> userCodeList = companyRelationUserPOList.stream().map(CompanyRelationUserPO::getUserCode).collect(Collectors.toList());
            userCodeListPlatformRequestVO.setUserCodeList(userCodeList);
            BusinessResponse<List<ApiUserAllInfoResponseVO>> userAllInfoList = getUserAllInfoList(userCodeListPlatformRequestVO);
            logger.info("getUserAllInfoList is :{}",userAllInfoList.getData().size());
            return userAllInfoList;
        }
        return BusinessResponse.ok(userBaseInfoResponseVOList);
    }

    /**
     * 根据企业编码获取旗下用户信息分页集合
     *
     * @param companyPageRequestVO
     */
    @Override
    public BusinessResponse<PageInfo<UserAdminListResponseVO>> getUserInfoListPageByCompanyCode(CompanyPageRequestVO companyPageRequestVO) {
        logger.info("visit UserService getUserInfoListPageByCompanyCode");
        List<UserAdminListResponseVO> userAdminListResponseVOList;
        PageInfo<UserAdminListResponseVO> userAdminListResponseVOPageInfo = new PageInfo<>();
        MPJLambdaWrapper<UserInfoPO> userInfoPOMPJLambdaWrapper = new MPJLambdaWrapper<UserInfoPO>().distinct();
        userInfoPOMPJLambdaWrapper.selectAll(UserInfoPO.class)
                .leftJoin(CompanyRelationUserPO.class,CompanyRelationUserPO::getUserCode,UserInfoPO::getUserCode)
                .eq(CompanyRelationUserPO::getCompanyCode,companyPageRequestVO.getCompanyCode())
                .eq(CompanyRelationUserPO::getStatus,CommonConstant.ENABLED)
                .eq(UserInfoPO::getStatus,CommonConstant.ENABLED);


        String username = companyPageRequestVO.getUsername();
        if (StringUtils.isNotBlank(username)){
            userInfoPOMPJLambdaWrapper.like(UserInfoPO::getUsername,username);
        }
        String email = companyPageRequestVO.getEmail();
        if (StringUtils.isNotBlank(email)){
            userInfoPOMPJLambdaWrapper.like(UserInfoPO::getEmail,email);
        }
        String mobile = companyPageRequestVO.getMobile();
        if (StringUtils.isNotBlank(mobile)){
            userInfoPOMPJLambdaWrapper.like(UserInfoPO::getMobile,mobile);
        }


        PageMethod.startPage(companyPageRequestVO.getPageNum(), companyPageRequestVO.getPageSize());
        List<UserInfoPO> companyRelationUserPOList = userInfoDAO.selectListDeep(userInfoPOMPJLambdaWrapper);
        logger.info("getUserInfoListByCompanyCode companyRelationUserDAO selectList size is :{}",companyRelationUserPOList.size());
        if (!companyRelationUserPOList.isEmpty()){
            userAdminListResponseVOList = BeanUtil.copyToList(companyRelationUserPOList, UserAdminListResponseVO.class);
            

            List<String> userCodeList = companyRelationUserPOList.stream().map(UserInfoPO::getUserCode).collect(Collectors.toList());
            LambdaQueryWrapper<CompanyAdministratorPO> companyAdministratorPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            companyAdministratorPOLambdaQueryWrapper.in(CompanyAdministratorPO::getUserCode,userCodeList)
                    .orderByDesc(CompanyAdministratorPO::getCreatedDate).select(CompanyAdministratorPO::getUserCode);
            List<Object> adminUserList = companyAdministratorDao.selectObjs(companyAdministratorPOLambdaQueryWrapper);
            if (!adminUserList.isEmpty()){
                for (UserAdminListResponseVO userAdminListResponseVO :userAdminListResponseVOList) {
                    //判断是否管理员
                    if (adminUserList.contains(userAdminListResponseVO.getUserCode())){
                        userAdminListResponseVO.setAdmin(CommonConstant.TRUE);
                    }
                }
            }
            //把管理员排在最上面
            userAdminListResponseVOList =  userAdminListResponseVOList.stream().sorted(Comparator.comparing(UserAdminListResponseVO::getAdmin)
                    .reversed()).collect(Collectors.toList());
            userAdminListResponseVOPageInfo = PageUtil.of(companyRelationUserPOList, userAdminListResponseVOList);
            //查询管理员，并且还要把放在最前面
            userAdminListResponseVOPageInfo.setList(userAdminListResponseVOList);
        }
        return BusinessResponse.ok(userAdminListResponseVOPageInfo);
    }

    /**
     * 批量获取用户信息
     *
     * @param userCodeListRequestVO
     */
    @Override
    public BusinessResponse<List<UserBaseInfoResponseVO>> getUserInfoList(UserCodeListRequestVO userCodeListRequestVO) {
        logger.info("visit UserService getUserInfoList");
        List<UserBaseInfoResponseVO> userBaseInfoResponseVOList = new ArrayList<>();
        List<String> userCodeList = userCodeListRequestVO.getUserCodeList();
        if (CollectionUtils.isNotEmpty(userCodeList)){
            userCodeList.forEach(e ->{
                    UserCodeRequestVO userCodeRequestVO = new UserCodeRequestVO();
                    userCodeRequestVO.setUserCode(e);
                try {
                    BusinessResponse<UserBaseInfoResponseVO> userInfo = getUserInfo(userCodeRequestVO);
                    userBaseInfoResponseVOList.add(userInfo.getData());
                } catch (Exception esg) {
                    logger.error("getUserInfo error is:{}",esg.toString());
                    //报错了要加标识给到其他服务
                    UserBaseInfoResponseVO userBaseInfoResponseVO = new UserBaseInfoResponseVO();
                    userBaseInfoResponseVO.setUserCode(e);
                    userBaseInfoResponseVO.setStatus(CommonConstant.DISABLED);
                    userBaseInfoResponseVOList.add(userBaseInfoResponseVO);
                }
            });

        }
        return BusinessResponse.ok(userBaseInfoResponseVOList);
    }

    /**
     * 更新用户基础信息
     *
     * @param userBaseInfoRequestVO
     */
    @Override
    public BusinessResponse<String> updateUserInfo(UserBaseInfoRequestVO userBaseInfoRequestVO) {
        logger.info("visit UserService updateUserInfo");
        String userCode = userBaseInfoRequestVO.getUserCode();
        //查询用户是否存在
        LambdaQueryWrapper<UserInfoPO> userInfoPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userInfoPOLambdaQueryWrapper.eq(UserInfoPO::getUserCode,userCode).eq(UserInfoPO::getStatus, CommonConstant.ENABLED);
        UserInfoPO userInfoPO = userInfoDAO.selectOne(userInfoPOLambdaQueryWrapper);
        logger.info("userInfoDAO selectOne result is :{}",userInfoPO);
        if (ObjectUtils.isEmpty(userInfoPO)){
            throw new BizException(ConstantStatusEnum.USER_DOES_NOT_EXIST.getCode(), ConstantStatusEnum.USER_DOES_NOT_EXIST.getDescCn());
        }
        BeanUtils.copyProperties(userBaseInfoRequestVO,userInfoPO);
        userInfoPO.setUsername(userBaseInfoRequestVO.getUserName());
        userInfoPO.setUpdatedBy(CommonConstant.ADMIN);
        userInfoPO.setUpdatedDate(LocalDateTime.now());
        logger.info("userInfoDAO updateById parameter is :{}",userInfoPO);
        int result = userInfoDAO.updateById(userInfoPO);
        logger.info("userInfoDAO updateById result is :{}",result);
        if (result == com.yhd.common.util.CommonConstant.ZERO){
            throw new BizException(ConstantStatusEnum.DATABASE_UPDATE_EXCEPTION.getCode(), ConstantStatusEnum.DATABASE_UPDATE_EXCEPTION.getDescCn());
        }

        //删除缓存
        redisUserUtils.deleteRedisUserInfo(userCode);
        return BusinessResponse.ok(CommonConstant.SUCCESS);
    }

    /**
     * 更新头像
     *
     * @param userHeadPortraitRequestVO
     */
    @Override
    public BusinessResponse<String> updateUserHeadPortrait(UserHeadPortraitRequestVO userHeadPortraitRequestVO) {
        logger.info("visit UserService updateUserInfo");
        //查询用户是否存在
        String userCode = userHeadPortraitRequestVO.getUserCode();
        queryUserIsExist(userCode);
        LambdaUpdateWrapper<UserInfoPO> userInfoPOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        userInfoPOLambdaUpdateWrapper.set(UserInfoPO::getAvatarPath,userHeadPortraitRequestVO.getFileUrl())
                .set(UserInfoPO::getUpdatedBy,CommonConstant.ADMIN).set(UserInfoPO::getUpdatedDate,LocalDateTime.now())
                .eq(UserInfoPO::getUserCode,userCode).eq(UserInfoPO::getStatus, CommonConstant.ENABLED);
        int result = userInfoDAO.update(null, userInfoPOLambdaUpdateWrapper);
        logger.info("userInfoDAO update HeadPortrait result is :{}",result);
        if (result == com.yhd.common.util.CommonConstant.ZERO){
            throw new BizException(ConstantStatusEnum.DATABASE_UPDATE_EXCEPTION.getCode(), ConstantStatusEnum.DATABASE_UPDATE_EXCEPTION.getDescCn());
        }
        //删除缓存
        redisUserUtils.deleteRedisUserInfo(userCode);
        return BusinessResponse.ok(CommonConstant.SUCCESS);
    }

    /**
     * 批量获取全部用户信息
     *
     * @param userCodeListPlatformRequestVO
     */
    @Override
    public BusinessResponse<List<ApiUserAllInfoResponseVO>> getUserAllInfoList(UserCodeListPlatformRequestVO userCodeListPlatformRequestVO) {
        logger.info("visit userService getUserAllInfoList ");
        List<String> userCodeList = userCodeListPlatformRequestVO.getUserCodeList();
        if (CollectionUtils.isEmpty(userCodeListPlatformRequestVO.getUserCodeList())){
            throw new BizException(ConstantStatusEnum.PARAM_NOT_EMPTY.getCode(), ConstantStatusEnum.PARAM_NOT_EMPTY.getDescCn());
        }
        List<ApiUserAllInfoResponseVO> apiUserAllInfoResponseVOList = new ArrayList<>();
        userCodeList.forEach(e ->{
            UserPlatformCodeRequestVO userPlatformCodeRequestVO= new UserPlatformCodeRequestVO();
            userPlatformCodeRequestVO.setUserCode(e);
            userPlatformCodeRequestVO.setPlatformCode(userCodeListPlatformRequestVO.getPlatformCode());
            try {
                BusinessResponse<ApiUserAllInfoResponseVO> apiUserBackstageAllInfo = getApiUserBackstageAllInfo(userPlatformCodeRequestVO);
                apiUserAllInfoResponseVOList.add(apiUserBackstageAllInfo.getData());
            } catch (Exception esg) {
                logger.error("getUserInfo error is:{}",esg.toString());
            }
        });
        return BusinessResponse.ok(apiUserAllInfoResponseVOList);
    }

    /**
     * 获取前台用户信息集合
     *
     * @param userCodeListPlatformRequestVO
     */
    @Override
    public BusinessResponse<List<ApiUserFrontInfoResponseVO>> getApiFrontInfoList(UserCodeListPlatformRequestVO userCodeListPlatformRequestVO) {
        logger.info("visit userService getUserAllInfoList ");
        List<String> userCodeList = userCodeListPlatformRequestVO.getUserCodeList();
        if (CollectionUtils.isEmpty(userCodeList)){
            throw new BizException(ConstantStatusEnum.PARAM_NOT_EMPTY.getCode(), ConstantStatusEnum.PARAM_NOT_EMPTY.getDescCn());
        }
        List<ApiUserFrontInfoResponseVO> apiUserFrontInfoResponseVOList = new ArrayList<>();
        userCodeList.forEach(e ->{
            UserPlatformCodeRequestVO userPlatformCodeRequestVO= new UserPlatformCodeRequestVO();
            userPlatformCodeRequestVO.setUserCode(e);
            userPlatformCodeRequestVO.setPlatformCode(userCodeListPlatformRequestVO.getPlatformCode());
            try {
                BusinessResponse<ApiUserFrontInfoResponseVO> apiFrontInfo = getApiFrontInfo(userPlatformCodeRequestVO);
                apiUserFrontInfoResponseVOList.add(apiFrontInfo.getData());
            } catch (Exception esg) {
                logger.error("getUserInfo error is:{}",esg.toString());
            }
        });
        return BusinessResponse.ok(apiUserFrontInfoResponseVOList);
    }

    /**
     * 查询用户全部信息以及线下企业信息(第三方调用)
     *
     * @param userPlatformCodeRequestVO
     */
    @Override
    public BusinessResponse<ApiUserAllInfoAndOfflineCompanyResponseVO> getUserAllInformationAndOfflineCompany(UserPlatformCodeRequestVO userPlatformCodeRequestVO) {
        logger.info("visit userService getUserAllInformationAndOfflineCompany ");
        ApiUserAllInfoAndOfflineCompanyResponseVO apiUserAllInfoAndOfflineCompanyResponseVO  = new ApiUserAllInfoAndOfflineCompanyResponseVO();
        BusinessResponse<ApiUserAllInfoResponseVO> apiUserBackstageAllInfo = getApiUserBackstageAllInfo(userPlatformCodeRequestVO);
        ApiUserAllInfoResponseVO apiUserAllInfoResponseVO = apiUserBackstageAllInfo.getData();
        apiUserAllInfoAndOfflineCompanyResponseVO.setApiUserAllInfoResponseVO(apiUserAllInfoResponseVO);
        String companyCode = apiUserAllInfoResponseVO.getCompanyCode();
        if (StringUtils.isNotBlank(companyCode)){
            OfflineCompanyInfoResponseVO offlineCompanyInfoResponseVO = companyService.queryOfflineCompany(companyCode, userPlatformCodeRequestVO.getPlatformCode());
            apiUserAllInfoAndOfflineCompanyResponseVO.setOfflineCompanyInfoResponseVO(offlineCompanyInfoResponseVO);
        }
        return BusinessResponse.ok(apiUserAllInfoAndOfflineCompanyResponseVO);
    }

    /**
     * 根据企业编码获取已加入旗下用户信息集合(分页)
     *
     * @param companyPageRequestVO
     */
    @Override
    public BusinessResponse<PageInfo<JoinCompanyUseListResponseVO>> getJoinCompanyUserInfoListPageByCompanyCode( CompanyPageRequestVO companyPageRequestVO) {
        logger.info("visit UserService getUserInfoListPageByCompanyCode");
        PageInfo<JoinCompanyUseListResponseVO> userAdminListResponseVOPageInfo = new PageInfo<>();

        String companyCode = companyPageRequestVO.getCompanyCode();
        String platformCode = "YHD-fa";
        LambdaQueryWrapper<RoleRelationCompanyPO> roleRelationCompanyPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        roleRelationCompanyPOLambdaQueryWrapper.eq(RoleRelationCompanyPO::getCompanyCode,companyCode).eq(RoleRelationCompanyPO::getPlatformCode,platformCode);
        RoleRelationCompanyPO roleRelationCompanyPO = roleRelationCompanyDAO.selectOne(roleRelationCompanyPOLambdaQueryWrapper);
        logger.info("getUserInfoListPageByCompanyCode roleRelationCompanyDAO selectOne is :{}",roleRelationCompanyPO);
        if (ObjectUtils.isEmpty(roleRelationCompanyPO)){
            throw new BizException(ConstantStatusEnum.DATABASE_DELETE_EXCEPTION.getCode(), ConstantStatusEnum.DATABASE_DELETE_EXCEPTION.getDescCn());
        }

        //查询企业角色是否有关联用户角色
        LambdaQueryWrapper<RolePO> rolePOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        rolePOLambdaQueryWrapper.eq(RolePO::getRelationRole,roleRelationCompanyPO.getRoleCode()).eq(RolePO::getRoleType,"user")
                .eq(RolePO::getStatus,CommonConstant.ENABLED);
        RolePO rolePO = roleDAO.selectOne(rolePOLambdaQueryWrapper);
        logger.info("getUserInfoListPageByCompanyCode roleDAO selectOne is :{}",rolePO);

        String roleCode = null;
        if (ObjectUtils.isNotEmpty(rolePO)){
            roleCode =  rolePO.getRoleCode();
        }
        PageMethod.startPage(companyPageRequestVO.getPageNum(), companyPageRequestVO.getPageSize());
        List<JoinCompanyUseListResponseVO> joinCompanyUseListResponseVOS = userInfoDAO.selectJoinCompanyMember(companyPageRequestVO,roleCode);
        joinCompanyUseListResponseVOS.forEach(e->{
            if (StringUtils.isNotBlank(e.getAvatarPath())){
                e.setAvatarPath(fontUrl + e.getAvatarPath());
            }
        });
        logger.info("getUserInfoListByCompanyCode companyRelationUserDAO selectList size is :{}",joinCompanyUseListResponseVOS.size());
        userAdminListResponseVOPageInfo = PageInfo.of(joinCompanyUseListResponseVOS);
        return BusinessResponse.ok(userAdminListResponseVOPageInfo);
    }

    /**
     * 根据用户名称查询用户列表,至多返回200条
     *
     * @param userName
     */
    @Override
    public BusinessResponse<List<UserInfoByUserNameResponseVO>> getUserByUsername(String userName) {
        logger.info("visit UserService getUserByUsername parameter is :{}",userName);
        if (StringUtils.isBlank(userName)){
            return BusinessResponse.fail(ConstantStatusEnum.PARAM_NOT_EMPTY.getCode(), ConstantStatusEnum.PARAM_NOT_EMPTY.getDescCn());
        }
        try {
            List<UserInfoByUserNameResponseVO> userInfoByUserNameResponseVOList = userInfoDAO.selectUserInfoByUserName(userName);
            return BusinessResponse.ok(userInfoByUserNameResponseVOList);
        } catch (Exception e) {
            logger.error("userInfoDAO selectUserInfoByUserName is ");
           throw new BizException(ConstantStatusEnum.DATABASE_QUERY_EXCEPTION.getCode(), ConstantStatusEnum.DATABASE_QUERY_EXCEPTION.getDescCn());
        }

    }

    @Override
    public BusinessResponse<List<String>> getUserCodeListByPlatformCode(PlatformCodePageRequest platformCodePageRequest) {
        LambdaQueryWrapper<PlatformRelationUserPO> platformRelationUserPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        platformRelationUserPOLambdaQueryWrapper.eq(PlatformRelationUserPO::getPlatformCode,platformCodePageRequest.getPlatformCode())
                        .orderByAsc(PlatformRelationUserPO::getCreatedDate)
                         .select(PlatformRelationUserPO::getUserCode);
        PageMethod.startPage(platformCodePageRequest.getPageNum(), platformCodePageRequest.getPageSize());
        List<PlatformRelationUserPO> platformRelationUserPOS = platformRelationUserDAO.selectList(platformRelationUserPOLambdaQueryWrapper);
        if (platformRelationUserPOS.isEmpty()){
            return BusinessResponse.ok(null);
        }
        List<String> userCodeList = platformRelationUserPOS.stream().map(PlatformRelationUserPO::getUserCode).collect(Collectors.toList());
        return BusinessResponse.ok(userCodeList);
    }


    /**
     * 判断绑定
     * @param userCode 用户编码
     * @param responseUserInfoVO 返回的基础信息
     * */
    private UserBaseInfoResponseVO setThirdParty(String userCode,UserBaseInfoResponseVO responseUserInfoVO){
        logger.info("UserService setThirdParty  parameter userCode is :{} responseUserBaseInfoVO, :{}",userCode,responseUserInfoVO);
        String isBindEmailString;
        String mobileCheckString;
        String email = responseUserInfoVO.getEmail();
        String mobile = responseUserInfoVO.getMobile();

        //查询邮箱是否绑定
        if (org.apache.commons.lang3.StringUtils.isNotBlank(email)){
            LambdaQueryWrapper<AccountPO> accountPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            accountPOLambdaQueryWrapper.eq(AccountPO::getUserCode,userCode).eq(AccountPO::getAccount,email)
                    .eq(AccountPO::getStatus,CommonConstant.ENABLED).eq(AccountPO::getType,CommonConstant.EMAIL);
            Long isBindEmail = accountDAO.selectCount(accountPOLambdaQueryWrapper);
            isBindEmailString =  isBindEmail.intValue() > com.yhd.common.util.CommonConstant.ZERO ? CommonConstant.TRUE : CommonConstant.FALSE;
            responseUserInfoVO.setEmailCheck(isBindEmailString);
        }else {
            responseUserInfoVO.setEmailCheck(CommonConstant.FALSE);
        }

        //查询手机是否绑定
        if (org.apache.commons.lang3.StringUtils.isNotBlank(mobile)){
            LambdaQueryWrapper<AccountPO> accountPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            accountPOLambdaQueryWrapper.eq(AccountPO::getUserCode,userCode).eq(AccountPO::getAccount,mobile)
                    .eq(AccountPO::getStatus,CommonConstant.ENABLED).eq(AccountPO::getType,CommonConstant.MOBILE);
            Long isBindMobile = accountDAO.selectCount(accountPOLambdaQueryWrapper);
            mobileCheckString =  isBindMobile > RedisConstant.ZERO ? CommonConstant.TRUE : CommonConstant.FALSE;
            responseUserInfoVO.setMobileCheck(mobileCheckString);
        }else {
            //未绑定
            responseUserInfoVO.setMobileCheck(CommonConstant.FALSE);
        }

        //判断QQ是否已绑定
        LambdaQueryWrapper<AccountQqPO> accountQqPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        accountQqPOLambdaQueryWrapper.eq(AccountQqPO::getUserCode,userCode).eq(AccountQqPO::getStatus,CommonConstant.ENABLED);
        try {
            AccountQqPO accountQqPO = accountQqDAO.selectOne(accountQqPOLambdaQueryWrapper);
            if (ObjectUtils.isNotEmpty(accountQqPO)){
                responseUserInfoVO.setQqCheck(CommonConstant.TRUE);
                responseUserInfoVO.setQqName(accountQqPO.getUserName());
                responseUserInfoVO.setQqOpenId(accountQqPO.getQqOpenid());
            }else {
                responseUserInfoVO.setQqCheck(CommonConstant.FALSE);
            }
        } catch (Exception e) {
            logger.error("accountQqDAO selectOne error is :{}",e.toString());
        }

        //判断微信是否已绑定
        LambdaQueryWrapper<AccountWechatPO> accountWechatPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        accountWechatPOLambdaQueryWrapper.eq(AccountWechatPO::getUserCode,userCode).eq(AccountWechatPO::getStatus,CommonConstant.ENABLED);
        try {
            AccountWechatPO accountWechatPO = accountWechatDAO.selectOne(accountWechatPOLambdaQueryWrapper);
            if (ObjectUtils.isNotEmpty(accountWechatPO)){
                responseUserInfoVO.setWechatCheck(CommonConstant.TRUE);
                responseUserInfoVO.setWechatName(accountWechatPO.getUserName());
                responseUserInfoVO.setWechatOpenId(accountWechatPO.getWechatOpenid());
            }else {
                responseUserInfoVO.setWechatCheck(CommonConstant.FALSE);
            }
        } catch (Exception e) {
            logger.error("accountWechatDAO selectOne error is :{}",e.toString());
        }
        return responseUserInfoVO;
    }


    /**
     * 获取控制的资源
     * @param codeMap     编码Map key：类型 value：编码
     * @param platformCode 平台编码
     * */
    private  List<InteriorResourcesResponseVO> getClosedResources(String platformCode, Map<String, String> codeMap){
        logger.info("getClosedResources parameter platformCode is  :{}",platformCode);
        List<InteriorResourcesResponseVO> interiorResourcesList = new ArrayList<>();
        //获取该平台下的所有资源
        List<ResourcesResponseVO> resourcesListByPlatform = resourcesService.getResourcesListByPlatform(platformCode);
        if (CollectionUtils.isNotEmpty(resourcesListByPlatform)){
            //查询禁用的权限
            List<String> closedResourcesList = resourcesClosedDimensionService.queryClosedDimensionListByPlatformAndCode(platformCode, codeMap);
            if (CollectionUtils.isNotEmpty(closedResourcesList)){
                //去重
                closedResourcesList = closedResourcesList.stream().distinct().collect(Collectors.toList());

                //过滤出符合的资源
                for (ResourcesResponseVO resourcesResponseVO : resourcesListByPlatform) {
                    if (closedResourcesList.contains(resourcesResponseVO.getResourcesCode())){
                        InteriorResourcesResponseVO interiorResources = new InteriorResourcesResponseVO();
                        interiorResources.setResourcesCode(resourcesResponseVO.getResourcesCode());
                        interiorResources.setResourcesName(resourcesResponseVO.getResourcesName());
                        interiorResources.setResourcesPath(resourcesResponseVO.getResourcesPath());
                        interiorResourcesList.add(interiorResources);
                    }
                }
            }
        }
        return interiorResourcesList;
    }


    /**
     * 获取用户所有信息
     * */
   private RedisUserAllInfoResponseVO getApiUserAllInfo(UserPlatformCodeRequestVO userPlatformCodeRequestVO){
       logger.info("visit ApiService getApiUserAllInfo");
       String userCode = userPlatformCodeRequestVO.getUserCode();
       String platformCode = userPlatformCodeRequestVO.getPlatformCode();
       String userAllInfoKey =  RedisConstant.YHD_SERVICE_BUC_USER_ALL_INFO + platformCode;
       RedisUserAllInfoResponseVO redisUserAllInfoResponseVO = new RedisUserAllInfoResponseVO();
       //判断平台是否有误

       //查缓存
       if (!redisService.hHasKey(userAllInfoKey,userCode)){
           UserCodeRequestVO userCodeRequestVO = new UserCodeRequestVO();
           //编码收集用来做权限判定
           HashMap<String, String> codeMap = new HashMap<>();

           codeMap.put(CommonConstant.USER,userCode);
           userCodeRequestVO.setUserCode(userCode);
           //查询用户基础信息
           BusinessResponse<UserBaseInfoResponseVO> userBaseInfoResponseVOBusinessResponse = getUserInfo(userCodeRequestVO);
           BeanUtils.copyProperties(userBaseInfoResponseVOBusinessResponse.getData(), redisUserAllInfoResponseVO);

           String companyCode = redisUserAllInfoResponseVO.getCompanyCode();
           if (StringUtils.isNotBlank(companyCode)){
               codeMap.put(CommonConstant.COMPANY,companyCode);

               //获取企业信息
               CompanyCodeRequestVO companyCodeRequestVO = new CompanyCodeRequestVO();
               companyCodeRequestVO.setCompanyCode(companyCode);
               BusinessResponse<CompanyResponseVO> companyResponseVOBusinessResponse = companyService.queryCompanyByCompanyCode(companyCodeRequestVO);
               BeanUtils.copyProperties(companyResponseVOBusinessResponse.getData(), redisUserAllInfoResponseVO);


               //查询是否管理员
               LambdaQueryWrapper<CompanyAdministratorPO> companyAdministratorPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
               companyAdministratorPOLambdaQueryWrapper.eq(CompanyAdministratorPO::getUserCode,userCode)
                       .eq(CompanyAdministratorPO::getCompanyCode,companyCode);
               Long adminCount = companyAdministratorDao.selectCount(companyAdministratorPOLambdaQueryWrapper);
               String admin  =  adminCount.intValue() == com.yhd.common.util.CommonConstant.ZERO  ?  com.yhd.common.util.CommonConstant.FALSE : com.yhd.common.util.CommonConstant.TRUE;
               redisUserAllInfoResponseVO.setAdmin(admin);
           }else {
               //没有企业信息直接管理员为false
               redisUserAllInfoResponseVO.setAdmin(CommonConstant.FALSE);
           }

           //获取角色信息
           AuthRequestVO authRequestVO = new AuthRequestVO();
           authRequestVO.setUserCode(userCode);
           authRequestVO.setPlatformCode(platformCode);
           BusinessResponse<RoleResponseVO> roleServiceRoleInfo = roleService.getRoleInfo(authRequestVO);
           RoleResponseVO roleResponseVO = roleServiceRoleInfo.getData();
           if (ObjectUtils.isNotEmpty(roleResponseVO)){
               BeanUtils.copyProperties(roleResponseVO, redisUserAllInfoResponseVO);
               //进了判空说明肯定有用户角色编码
               codeMap.put(CommonConstant.USER_ROLE,roleResponseVO.getUserRole());
               //判断一下是否有企业角色
               if (StringUtils.isNotBlank(roleResponseVO.getCompanyRole())){
                   codeMap.put(CommonConstant.COMPANY_ROLE,roleResponseVO.getCompanyRole());
               }
           }

           //获取资源权限信息
           codeMap.put(CommonConstant.PLATFORM,platformCode);
           List<InteriorResourcesResponseVO> interiorResources = getClosedResources(platformCode, codeMap);

           //获取平台所有权限
           List<ResourcesResponseVO> resourcesList = resourcesService.getResourcesListByPlatform(platformCode);
           List<ResourcesAuthResponseVO> resourcesAuthResponseVOList = new ArrayList<>();
           if (CollectionUtils.isNotEmpty(resourcesList)){
               //获取禁用的权限编码
               List<String> resourcesClosedList = interiorResources.stream().map(InteriorResourcesResponseVO::getResourcesCode).collect(Collectors.toList());
               for (ResourcesResponseVO resourcesResponseVO :resourcesList) {
                   ResourcesAuthResponseVO resourcesAuthResponseVO = new ResourcesAuthResponseVO();
                   resourcesAuthResponseVO.setResourcesCode(resourcesResponseVO.getResourcesCode());
                   resourcesAuthResponseVO.setResourcesName(resourcesResponseVO.getResourcesName());
                   //判断是否有禁用
                   if (resourcesClosedList.contains(resourcesResponseVO.getResourcesCode())){
                       resourcesAuthResponseVO.setStatus(CommonConstant.DISABLED);
                   }else {
                       resourcesAuthResponseVO.setStatus(CommonConstant.ENABLED);
                   }
                   resourcesAuthResponseVOList.add(resourcesAuthResponseVO);
               }
           }
           redisUserAllInfoResponseVO.setResourcesAuthResponseVOList(resourcesAuthResponseVOList);
           redisUserAllInfoResponseVO.setResourcesClosedList(interiorResources);
           redisUserAllInfoResponseVO.setPlatformCode(platformCode);

           //根据平台查询微信公众号
           LambdaQueryWrapper<WechatOfficialPO> wechatOfficialPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
           wechatOfficialPOLambdaQueryWrapper
                   .eq(WechatOfficialPO::getUserCode,userCode).eq(WechatOfficialPO::getPlatformCode,platformCode);
           try {
               WechatOfficialPO wechatOfficialPO = wechatOfficialDAO.selectOne(wechatOfficialPOLambdaQueryWrapper);
               logger.info("wechatOfficialDAO selectOne result is :{}",wechatOfficialPO);
               if (ObjectUtils.isNotEmpty(wechatOfficialPO)){
                   redisUserAllInfoResponseVO.setWechatOfficialOpenId(wechatOfficialPO.getOpenid());
                   redisUserAllInfoResponseVO.setWechatOfficialCheck(com.yhd.common.util.CommonConstant.TRUE);
               }else {
                   redisUserAllInfoResponseVO.setWechatOfficialCheck(com.yhd.common.util.CommonConstant.FALSE);
               }
           } catch (Exception e) {
               logger.error("wechatOfficialDAO selectOne result is :{}",e.toString());
           }
           //缓存到Redis
           String apiUserAllInfoResponseVOJson = JSONUtil.toJson(redisUserAllInfoResponseVO);
           logger.info("get apiUserAllInfoResponseVOJson is :{}",apiUserAllInfoResponseVOJson);
           Boolean result = redisService.hset(userAllInfoKey,userCode,apiUserAllInfoResponseVOJson);
           logger.info("redis set apiUserAllInfoResponseVOJson result :{}",result);
       }else {
           redisUserAllInfoResponseVO =  JSONUtil.fromObject(redisService.hget(userAllInfoKey,userCode).toString(), RedisUserAllInfoResponseVO.class);
       }
       return redisUserAllInfoResponseVO;
   }

   /**
    * 设置企业状态
    * */
   private void setCompanyStatus(String userCode, UserBaseInfoResponseVO responseUserInfo){
       //查询是否有企业
       CompanyPO companyPO = companyService.queryCompanyByUserCode(userCode);
       if (ObjectUtils.isNotEmpty(companyPO)){
           responseUserInfo.setCompanyCode(companyPO.getCompanyCode());
           responseUserInfo.setCompanyName(companyPO.getCompanyName());


           //如果有企业要查询企业是否在认证审核
           LambdaQueryWrapper<CompanyReviewPO> companyReviewPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
           companyReviewPOLambdaQueryWrapper.eq(CompanyReviewPO::getErpCompanyCode,companyPO.getErpCompanyCode())
                   .eq(CompanyReviewPO::getType,CommonConstant.AUTHENTICATION)
                   .eq(CompanyReviewPO::getReviewStatus,CommonConstant.UNREVIEWED)
                   .eq(CompanyReviewPO::getSubmitUserCode,userCode);
           Long existAuthentication = companyReviewDao.selectCount(companyReviewPOLambdaQueryWrapper);
           if (existAuthentication.intValue() > com.yhd.common.util.CommonConstant.ZERO){
               responseUserInfo.setCompanyStatus("audit");
           }else {
               //查询用户是否正在加入认证企业
               isProcessing(responseUserInfo,userCode);
           }
       }else {
           //如果没有企业要查询企业是否在申请建档
           LambdaQueryWrapper<CompanyReviewPO> companyReviewPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
           companyReviewPOLambdaQueryWrapper.eq(CompanyReviewPO::getSubmitUserCode,userCode)
                   .eq(CompanyReviewPO::getType,CommonConstant.ORDINARY).eq(CompanyReviewPO::getReviewStatus,CommonConstant.UNREVIEWED);
           try {
               CompanyReviewPO companyReviewPO = companyReviewDao.selectOne(companyReviewPOLambdaQueryWrapper);
               if (ObjectUtils.isNotEmpty(companyReviewPO)){
                   responseUserInfo.setCompanyStatus("filing");
               }else {
                   //查询用户是否正在加入认证企业
                   isProcessing(responseUserInfo,userCode);
               }
           } catch (Exception e) {
               logger.error("companyReviewDao selectOne error is :{}",e.toString());
           }
           responseUserInfo.setCompanyName(CommonConstant.NO_ENTERPRISE);
       }
   }

   /**
    * 查询用户是否存在
    * */
   private void queryUserIsExist(String userCode){
        logger.info("queryUserIsExist parameter is :{}",userCode);
       LambdaQueryWrapper<UserInfoPO> userInfoPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
       userInfoPOLambdaQueryWrapper.eq(UserInfoPO::getUserCode,userCode).eq(UserInfoPO::getStatus, CommonConstant.ENABLED);
       Long count = userInfoDAO.selectCount(userInfoPOLambdaQueryWrapper);
       if (count.intValue() == com.yhd.common.util.CommonConstant.ZERO){
           throw new BizException(ConstantStatusEnum.USER_DOES_NOT_EXIST.getCode(), ConstantStatusEnum.USER_DOES_NOT_EXIST.getDescCn());
       }
   }

    /**
     *查询用户是否正在加入认证企业
     */
   private void isProcessing(UserBaseInfoResponseVO responseUserInfo,String userCode){
       logger.info("visit isProcessing method");
       LambdaQueryWrapper<AdminReviewRecordPO> adminReviewRecordPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
       adminReviewRecordPOLambdaQueryWrapper.eq(AdminReviewRecordPO::getStatus,CommonConstant.WAIT_STATUS).eq(AdminReviewRecordPO::getUserCode,userCode);
       AdminReviewRecordPO adminReviewRecordPO = adminReviewRecordDAO.selectOne(adminReviewRecordPOLambdaQueryWrapper);
       logger.info("adminReviewRecordDAO selectOne adminReviewRecordPO :{}",adminReviewRecordPO);
       //没有申请记录
       if (ObjectUtils.isEmpty(adminReviewRecordPO)){
           //正常情况
           responseUserInfo.setCompanyStatus(CommonConstant.NORMAL);
       }else {
           //加入认证企业申请钟
           responseUserInfo.setCompanyStatus("processing");
       }

   }
}
