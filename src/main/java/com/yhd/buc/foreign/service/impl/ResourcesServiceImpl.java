package com.yhd.buc.foreign.service.impl;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yhd.buc.foreign.constant.CommonConstant;
import com.yhd.buc.foreign.constant.RedisConstant;
import com.yhd.buc.foreign.dao.ResourcesConfigDao;
import com.yhd.buc.foreign.dao.ResourcesDao;
import com.yhd.buc.foreign.pojo.po.ResourcesConfigPO;
import com.yhd.buc.foreign.pojo.po.ResourcesPO;
import com.yhd.buc.foreign.pojo.vo.response.ResourcesResponseVO;
import com.yhd.buc.foreign.service.api.ResourcesService;
import com.yhd.common.json.JSONUtil;
import com.yhd.common.util.LogUtils;
import com.yhd.redis.service.RedisService;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id:PlatformService.java, v 0.12023/3/21 15:23 kangrong Exp $
 */
@Service
public class ResourcesServiceImpl implements ResourcesService {

    private static final Logger logger = LogUtils.getLogger();

    @Resource
    private RedisService redisService;

    @Resource
    private ResourcesConfigDao resourcesConfigDao;

    @Resource
    private ResourcesDao resourcesDao;

    /**
     * 获取根据平台编码所有资源
     *
     * @param platformCode 平台编码
     */
    @Override
    public List<ResourcesResponseVO> getResourcesListByPlatform(String platformCode) {
        logger.info("ResourcesService getResourcesListByPlatform parameter is :{}",platformCode);

        List<ResourcesResponseVO> resourcesResponseVOList = new ArrayList<>();
        //查询平台资源缓存
        if (!redisService.hHasKey(RedisConstant.YHD_SERVICE_BUC_PLATFORM_RESOURCES,platformCode)){

            //查询是否有配置平台资源
            LambdaQueryWrapper<ResourcesConfigPO> resourcesConfigPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            resourcesConfigPOLambdaQueryWrapper.eq(ResourcesConfigPO::getPlatformCode,platformCode)
                    .eq(ResourcesConfigPO::getStatus, CommonConstant.ENABLED);
            ResourcesConfigPO resourcesConfigPO = resourcesConfigDao.selectOne(resourcesConfigPOLambdaQueryWrapper);
            logger.info("getResourcesListByPlatform resourcesConfigDao selectOne result is :{}",resourcesConfigPO);
            if (ObjectUtils.isEmpty(resourcesConfigPO)){
                setRedis(resourcesResponseVOList,platformCode);
                return resourcesResponseVOList;
            }

            //查询平台下有多少启用资源
            LambdaQueryWrapper<ResourcesPO> resourcesPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            resourcesPOLambdaQueryWrapper.eq(ResourcesPO::getConfigId,resourcesConfigPO.getId())
                    .eq(ResourcesPO::getStatus,CommonConstant.ENABLED);
            List<ResourcesPO> resourcesPOList = resourcesDao.selectList(resourcesPOLambdaQueryWrapper);
            logger.info("resourcesDao selectList resourcesPOList result is :{}",resourcesPOList);
            if (CollectionUtils.isNotEmpty(resourcesPOList)){
                resourcesResponseVOList = BeanUtil.copyToList(resourcesPOList, ResourcesResponseVO.class);
            }
            setRedis(resourcesResponseVOList,platformCode);
        }else {
            resourcesResponseVOList = cn.hutool.json.JSONUtil.toList(redisService.hget(RedisConstant.YHD_SERVICE_BUC_PLATFORM_RESOURCES, platformCode).toString(), ResourcesResponseVO.class);
            logger.info("redis get YHD_SERVICE_BUC_PLATFORM_RESOURCES key is :{} resourcesResponseVOListSize is :{}",platformCode,resourcesResponseVOList.size());
        }

        return resourcesResponseVOList;
    }


    /**
     * 设置redis存入
     * @param resourcesResponseVOList 资源集合
     * @param platformCode 平台编码
     *
     * */
    private void setRedis( List<ResourcesResponseVO> resourcesResponseVOList ,String platformCode){
        String resourcesResponseVOListJson = JSONUtil.toJson(resourcesResponseVOList);
        logger.info("redisService set YHD_SERVICE_BUC_PLATFORM_RESOURCES platformCode :{} json is :{}",platformCode,resourcesResponseVOListJson);
        Boolean setResourcesResult = redisService.hset(RedisConstant.YHD_SERVICE_BUC_PLATFORM_RESOURCES, platformCode, resourcesResponseVOListJson);
        logger.info("redisService set YHD_SERVICE_BUC_PLATFORM_RESOURCES result :{}",setResourcesResult);
    }
}
