package com.yhd.buc.foreign.service.api;

import com.yhd.buc.foreign.pojo.vo.request.AuthRequestVO;
import com.yhd.buc.foreign.pojo.vo.response.RoleResponseVO;
import com.yhd.common.pojo.vo.BusinessResponse;

/**
 * <AUTHOR>
 * @version Id:RoleService.java, v 0.12022/7/20 9:45 kangrong Exp $
 */
public interface RoleService {


    /**
     * 获取用户角色
     * */
    BusinessResponse<RoleResponseVO> getRoleInfo(AuthRequestVO requestGetAuthVO);
}
