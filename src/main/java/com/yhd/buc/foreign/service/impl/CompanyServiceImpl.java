package com.yhd.buc.foreign.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yhd.buc.foreign.constant.CommonConstant;
import com.yhd.buc.foreign.constant.RedisConstant;
import com.yhd.buc.foreign.dao.*;
import com.yhd.buc.foreign.enums.ConstantStatusEnum;
import com.yhd.buc.foreign.pojo.po.*;
import com.yhd.buc.foreign.pojo.vo.request.CompanyCodeListRequestVO;
import com.yhd.buc.foreign.pojo.vo.request.CompanyCodeRequestVO;
import com.yhd.buc.foreign.pojo.vo.request.ERPCompanyCodeListRequestVO;
import com.yhd.buc.foreign.pojo.vo.request.OfflineCompanyCodeListRequestVO;
import com.yhd.buc.foreign.pojo.vo.response.*;
import com.yhd.buc.foreign.service.api.CompanyService;
import com.yhd.buc.foreign.service.api.PlatformService;
import com.yhd.common.exception.BizException;
import com.yhd.common.json.JSONUtil;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.redis.service.RedisService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.yhd.buc.foreign.constant.RedisConstant.YHD_SERVICE_BUC_OFFLINE_COMPANY_INFO;

/**
 * <AUTHOR>
 * @version Id:CompanyServiceImpl.java, v 0.12023/3/20 11:18 kangrong Exp $
 */
@Service
public class CompanyServiceImpl implements CompanyService {

    private static final Logger logger = LogUtils.getLogger();

    @Resource
    private CompanyDAO companyDAO;

    @Resource
    private CompanyRelationUserDAO companyRelationUserDAO;

    @Resource
    private PlatformService platformService;

    @Resource
    private PlatformDao platformDao;

    @Resource
    private SyncCustomerInfoPrivateDao syncCustomerInfoPrivateDao;

    @Resource
    private UcSyncCustomerInfoDao syncCustomerInfoDao;

    @Resource
    private UcSyncMonthlySettleDao ucSyncMonthlySettleDao;

    @Resource
    private CompanyMerchandiserDAO companyMerchandiserDAO;

    @Resource
    private CompanyMerchandiserSalesmanInfoDAO companyMerchandiserSalesmanInfoDAO;

    @Resource
    private SyncEmployeeDAO syncEmployeeDAO;

    @Resource
    private SyncCorrespondingMerchandiserDao syncCorrespondingMerchandiserDao;

    @Resource
    private RedisService redisService;

    //上传系统/模块名称
    @Value("${oss.frontUrl}")
    private String fontUrl;

    /**
     * 根据用户编码返回关联的企业
     *
     * @param userCode
     */
    @Override
    public CompanyPO queryCompanyByUserCode(String userCode) {
        logger.info("visit CompanyService  queryCompanyByUserCode parameter is :{}",userCode);
        CompanyPO companyPO = null;

        //查询用户企业关联表
        LambdaQueryWrapper<CompanyRelationUserPO> companyRelationUserQueryWrapper = new LambdaQueryWrapper<>();
        companyRelationUserQueryWrapper.eq(CompanyRelationUserPO::getUserCode,userCode).eq(CompanyRelationUserPO::getStatus, CommonConstant.ENABLED);
        CompanyRelationUserPO companyRelationUserPO = companyRelationUserDAO.selectOne(companyRelationUserQueryWrapper);
        logger.info("queryCompanyByUserCode companyRelationUserDAO selectOne result is :{}",companyRelationUserPO);

        //关联表不为空则查询企业
        if (ObjectUtils.isNotEmpty(companyRelationUserPO)){
             companyPO = queryCompanyPOByCode(companyRelationUserPO.getCompanyCode());
        }
        return companyPO;
    }

    /**
     * 查询线上企业信息
     *
     * @param requestCompanyVO
     */
    @Override
    public BusinessResponse<CompanyResponseVO> queryCompanyByCompanyCode(CompanyCodeRequestVO requestCompanyVO) {
        logger.info("enjoy CompanyService queryUserByCompanyCode");
        String companyCode = requestCompanyVO.getCompanyCode();

        logger.info("get CompanyVO redis key is :{}",RedisConstant.YHD_SERVICE_BUC_ONLINE_COMPANY_INFO);
        CompanyResponseVO responseCompanyVO = new CompanyResponseVO();
        if (!redisService.hHasKey(RedisConstant.YHD_SERVICE_BUC_ONLINE_COMPANY_INFO,companyCode)){
            CompanyPO companyPO = queryCompanyPOByCode(companyCode);
            BeanUtils.copyProperties(companyPO,responseCompanyVO);
            responseCompanyVO.setTestCompany(companyPO.getIsTest());
            String responseCompanyVOJson = JSONUtil.toJson(responseCompanyVO);
            logger.info("get responseCompanyVOJson is :{}",responseCompanyVOJson);
            Boolean result = redisService.hset(RedisConstant.YHD_SERVICE_BUC_ONLINE_COMPANY_INFO,companyCode,responseCompanyVOJson);
            logger.info("redis set responseCompanyVOJson result :{}",result);
        }
        else {
            responseCompanyVO = JSONUtil.fromObject(redisService.hget(RedisConstant.YHD_SERVICE_BUC_ONLINE_COMPANY_INFO,companyCode).toString(),CompanyResponseVO.class);
            logger.info("redis get responseCompanyVO is :{}",responseCompanyVO);
        }
        return BusinessResponse.ok(responseCompanyVO);
    }

    /**
     * 批量查询线下企业信息
     *
     * @param requestCompanyCodeListVO
     */
    @Override
    public BusinessResponse<List<OfflineCompanyInfoResponseVO>> queryOfflineCompanyListByCompanyCodeList(CompanyCodeListRequestVO requestCompanyCodeListVO) {
        logger.info("visit CompanyService queryOfflineCompanyListByCompanyCodeList");
        String platformCode = requestCompanyCodeListVO.getPlatformCode();
        List<Object> platformCodeList = platformService.queryPlatformCodeList();
        if (!platformCodeList.contains(platformCode)){
            //平台不合法
            throw new BizException(ConstantStatusEnum.PLATFORM_HAS_BEEN_DISABLED.getCode(), ConstantStatusEnum.PLATFORM_HAS_BEEN_DISABLED.getDescCn());
        }
        List<OfflineCompanyInfoResponseVO> offlineCompanyInfoResponseVOList = new ArrayList<>();
        List<String> companyCodeList = requestCompanyCodeListVO.getCompanyCodeList();
        for (String companyCode :companyCodeList) {
            OfflineCompanyInfoResponseVO offlineCompanyInfoResponseVO = queryOfflineCompany(companyCode, platformCode);
            if (StringUtils.isNotBlank(offlineCompanyInfoResponseVO.getErpCompanyCode())){
                offlineCompanyInfoResponseVOList.add(offlineCompanyInfoResponseVO);
            }

        }
        logger.info("get offlineCompanyInfoResponseVOList size is :{}",offlineCompanyInfoResponseVOList.size());
        return BusinessResponse.ok(offlineCompanyInfoResponseVOList);
    }


    /**
     * 根据企业编码查询企业信息
     * @param companyCode 企业编码
     * */
    private CompanyPO queryCompanyPOByCode(String companyCode){
        logger.info("queryCompanyPOByCode parameter is:");
        LambdaQueryWrapper<CompanyPO> companyPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        companyPOLambdaQueryWrapper.eq(CompanyPO::getCompanyCode,companyCode)
                .eq(CompanyPO::getStatus,CommonConstant.ENABLED);
        CompanyPO companyPO =  companyDAO.selectOne(companyPOLambdaQueryWrapper);
        logger.info("queryCompanyByUserCode companyDAO selectOne result is :{}",companyPO);
        if (ObjectUtils.isEmpty(companyPO)){
            throw new BizException(ConstantStatusEnum.COMPANY_HAS_BEEN_DISABLED.getCode(), ConstantStatusEnum.COMPANY_HAS_BEEN_DISABLED.getDescCn());
        }
        return companyPO;
    }


    /**
     *
     * 查询线下企业
     * @param companyCode 企业编码
     * @param platformCode 平台编码编码
     * */
    @Override
    public  OfflineCompanyInfoResponseVO queryOfflineCompany(String companyCode,String platformCode){
        logger.info("CompanyService queryOfflineCompany parameter companyCode is :{} platformCode is:{}",companyCode,platformCode);
        String key  = YHD_SERVICE_BUC_OFFLINE_COMPANY_INFO + platformCode;
        OfflineCompanyInfoResponseVO offlineCompanyInfoResponseVO = new OfflineCompanyInfoResponseVO();
        if (!redisService.hHasKey(key,companyCode)){
            LambdaQueryWrapper<PlatformPO> platformPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            platformPOLambdaQueryWrapper.eq(PlatformPO::getStatus,CommonConstant.ENABLED).eq(PlatformPO::getPlatformCode,platformCode);
            PlatformPO platformPO = platformDao.selectOne(platformPOLambdaQueryWrapper);
            //有判断平台，所以不会为空
            String sonCompanyCode = platformPO.getCompanyCode();

            CompanyPO companyPO = queryCompanyPOByCode(companyCode);
            String erpCompanyCode = companyPO.getErpCompanyCode();
            //查询线下企业细表
            offlineCompanyInfoResponseVO.setCompanyCode(companyCode);
            offlineCompanyInfoResponseVO.setErpCompanyCode(erpCompanyCode);
            offlineCompanyInfoResponseVO.setCompanyIdentity(sonCompanyCode);
            offlineCompanyInfoResponseVO.setCompanyName(companyPO.getCompanyName());
            //查询主表
            LambdaQueryWrapper<UcSyncCustomerInfoPO> ucSyncCustomerInfoPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            ucSyncCustomerInfoPOLambdaQueryWrapper.eq(UcSyncCustomerInfoPO::getCustomerNo,erpCompanyCode);
            UcSyncCustomerInfoPO ucSyncCustomerInfoPO = syncCustomerInfoDao.selectOne(ucSyncCustomerInfoPOLambdaQueryWrapper);
            if (ObjectUtils.isEmpty(ucSyncCustomerInfoPO)){
                //存入redis
                setRedis(offlineCompanyInfoResponseVO,key,companyCode);
                //线下客户细表没有数据说明，线下没有这件企业，返回线上数据即可
                return offlineCompanyInfoResponseVO;
            }
            
            //设置归属地、含税价格属性、以及更新时间
            String ownershipCompany = ucSyncCustomerInfoPO.getOwnershipCompany();
            if (StringUtils.isBlank(ownershipCompany)){
                //如果为空默认为东莞怡合达
                ownershipCompany = "DGYHD";
            }
            offlineCompanyInfoResponseVO.setOwnershipCompany(ownershipCompany);
            offlineCompanyInfoResponseVO.setTaxPriceAttribute(ucSyncCustomerInfoPO.getTaxPriceAttribute() == null ? com.yhd.common.util.CommonConstant.TWO : ucSyncCustomerInfoPO.getTaxPriceAttribute() );
            offlineCompanyInfoResponseVO.setTaxPriceAuditDate(ucSyncCustomerInfoPO.getTaxPriceAuditDate());
            offlineCompanyInfoResponseVO.setCompanyAddress(ucSyncCustomerInfoPO.getRegistryAddress());
            
            
            LambdaQueryWrapper<SyncCustomerInfoPrivatePO> syncCustomerInfoPrivatePOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            syncCustomerInfoPrivatePOLambdaQueryWrapper.eq(SyncCustomerInfoPrivatePO::getCompanyCode,sonCompanyCode)
                    .eq(SyncCustomerInfoPrivatePO::getCustomerNo,erpCompanyCode);
            SyncCustomerInfoPrivatePO syncCustomerInfoPrivatePO = syncCustomerInfoPrivateDao.selectOne(syncCustomerInfoPrivatePOLambdaQueryWrapper);
            logger.info("syncCustomerInfoPrivateDao selectOne result is :{}",syncCustomerInfoPrivatePO);
            if (ObjectUtils.isEmpty(syncCustomerInfoPrivatePO)){
                //存入redis
                setRedis(offlineCompanyInfoResponseVO,key,companyCode);
                //线下客户细表没有数据说明，线下没有这件企业，返回线上数据即可
                return offlineCompanyInfoResponseVO;
            }
            if (com.yhd.common.util.CommonConstant.TWO.equals(syncCustomerInfoPrivatePO.getTransactionStatus())
                    || com.yhd.common.util.CommonConstant.ONE.equals(syncCustomerInfoPrivatePO.getExceptionType())){
                //当交易状况为暂停交易或者异常类型为暂停下单则该企业不允许下单，为空的话是默认正常
                offlineCompanyInfoResponseVO.setTradingStatus(com.yhd.common.util.CommonConstant.TWO);
            }else {
                offlineCompanyInfoResponseVO.setTradingStatus(com.yhd.common.util.CommonConstant.ONE);
            }

            offlineCompanyInfoResponseVO.setErpCompanyCode(erpCompanyCode);
            offlineCompanyInfoResponseVO.setCompanyCode(companyCode);

            //如果为空就给她一个4
            Integer discountLevel = syncCustomerInfoPrivatePO.getDiscountLevel();
            if (discountLevel == null || com.yhd.common.util.CommonConstant.ZERO.equals(discountLevel)){
                discountLevel = com.yhd.common.util.CommonConstant.FOUR;
            }
            offlineCompanyInfoResponseVO.setDiscountLevel(discountLevel);

            //查询月结表
            LambdaQueryWrapper<UcSyncMonthlySettlePO> ucSyncMonthlySettlePOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            ucSyncMonthlySettlePOLambdaQueryWrapper.eq(UcSyncMonthlySettlePO::getCompanyCode,sonCompanyCode)
                    .eq(UcSyncMonthlySettlePO::getCustomerNo,syncCustomerInfoPrivatePO.getCustomerNo());
            UcSyncMonthlySettlePO ucSyncMonthlySettlePO = ucSyncMonthlySettleDao.selectOne(ucSyncMonthlySettlePOLambdaQueryWrapper);
            logger.info("ucSyncMonthlySettleDao selectOne result is :{}",ucSyncMonthlySettlePO);
            if (ObjectUtils.isNotEmpty(ucSyncMonthlySettlePO)){
                offlineCompanyInfoResponseVO.setSettlementMethod(ucSyncMonthlySettlePO.getSettlementType());
            }

            //查询FA是否有设置的专属跟单
            String salesNo = syncCustomerInfoPrivatePO.getSalesNo();
            LambdaQueryWrapper<SyncEmployeePO> saleEmployeePOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            saleEmployeePOLambdaQueryWrapper
                    .eq(SyncEmployeePO::getEmployeeCode,salesNo)
                    .and(ss -> ss.eq(SyncEmployeePO::getStatus,CommonConstant.NORMAL).or().eq(SyncEmployeePO::getStatus,"caller"));


            //查询业务员是否离职(caller是访客状态)
            SyncEmployeePO saleEmployeePO = syncEmployeeDAO.selectOne(saleEmployeePOLambdaQueryWrapper);
            logger.info("syncEmployeeDAO selectOne saleEmployeePO is :{}",saleEmployeePO);
            if (ObjectUtils.isEmpty(saleEmployeePO)){
                //缓存Redis
                setRedis(offlineCompanyInfoResponseVO,key,companyCode);
                return offlineCompanyInfoResponseVO;
            }else {
                offlineCompanyInfoResponseVO.setOperatorJobNumber(saleEmployeePO.getEmployeeCode());
                offlineCompanyInfoResponseVO.setOperatorName(saleEmployeePO.getEmployeeName());
                offlineCompanyInfoResponseVO.setOperatorPost(saleEmployeePO.getUnitName());
                //有公司座机填公司座机
                if (StringUtils.isBlank(saleEmployeePO.getCompanyMobile())){
                    offlineCompanyInfoResponseVO.setOperatorMobile(saleEmployeePO.getMobile());
                }else {
                    offlineCompanyInfoResponseVO.setOperatorMobile(saleEmployeePO.getCompanyMobile());
                }
            }


            //查询业务员QQ和邮箱
            LambdaQueryWrapper<CompanyMerchandiserSalesmanInfoPO> salesmanInfoPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            salesmanInfoPOLambdaQueryWrapper.eq(CompanyMerchandiserSalesmanInfoPO::getEmployeeCode,salesNo);
            CompanyMerchandiserSalesmanInfoPO salesmanInfoPO = companyMerchandiserSalesmanInfoDAO.selectOne(salesmanInfoPOLambdaQueryWrapper);
            logger.info("companyMerchandiserSalesmanInfoDAO selectOne salesmanInfoPO result is:{}",salesmanInfoPO);
            if (ObjectUtils.isNotEmpty(salesmanInfoPO)){
                offlineCompanyInfoResponseVO.setOperatorEmail(salesmanInfoPO.getEmail());
                offlineCompanyInfoResponseVO.setOperatorQq(salesmanInfoPO.getQq());
                if (StringUtils.isNotBlank(salesmanInfoPO.getEnterpriseWechatCodeUrl())){
                    offlineCompanyInfoResponseVO.setOperatorEnterpriseWechatCodeUrl(fontUrl + salesmanInfoPO.getEnterpriseWechatCodeUrl());
                }
            }




            //查询是否有专属跟单
            LambdaQueryWrapper<CompanyMerchandiserPO> companyMerchandiserPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            companyMerchandiserPOLambdaQueryWrapper.eq(CompanyMerchandiserPO::getPlatformCode,platformCode)
                    .eq(CompanyMerchandiserPO::getCompanyCode,companyCode).eq(CompanyMerchandiserPO::getSalesmanCode,salesNo);
            CompanyMerchandiserPO companyMerchandiserPO = companyMerchandiserDAO.selectOne(companyMerchandiserPOLambdaQueryWrapper);
            logger.info("companyMerchandiserDAO selectOne companyMerchandiserPO is :{}",companyMerchandiserPO);
            if (ObjectUtils.isEmpty(companyMerchandiserPO)){

                //设置业务员信息到跟单员
                copySaleToMerchandiserInfo(offlineCompanyInfoResponseVO,key,companyCode);

                return offlineCompanyInfoResponseVO;
            }

            String merchandiserCode = companyMerchandiserPO.getMerchandiserCode();
            //查看业务员跟单员线下表是否有关联
            LambdaQueryWrapper<SyncCorrespondingMerchandiserPO> syncCorrespondingMerchandiserPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            syncCorrespondingMerchandiserPOLambdaQueryWrapper.eq(SyncCorrespondingMerchandiserPO::getEmpNo,merchandiserCode)
                    .eq(SyncCorrespondingMerchandiserPO::getSalesNo,salesNo);

            Long count = syncCorrespondingMerchandiserDao.selectCount(syncCorrespondingMerchandiserPOLambdaQueryWrapper);
            if (count.intValue() == com.yhd.common.util.CommonConstant.ZERO){

                //设置业务员信息到跟单员
                 copySaleToMerchandiserInfo(offlineCompanyInfoResponseVO,key,companyCode);

                return offlineCompanyInfoResponseVO;
            }

            //查看员工是否离职
            LambdaQueryWrapper<SyncEmployeePO> merchandiserEmployeePOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            merchandiserEmployeePOLambdaQueryWrapper.eq(SyncEmployeePO::getEmployeeCode,merchandiserCode)
                    .and(ss -> ss.eq(SyncEmployeePO::getStatus,CommonConstant.NORMAL).or().eq(SyncEmployeePO::getStatus,"caller"));
            SyncEmployeePO syncEmployeePO = syncEmployeeDAO.selectOne(merchandiserEmployeePOLambdaQueryWrapper);
            logger.info("syncEmployeeDAO selectOne syncEmployeePO is :{}",companyMerchandiserPO);
            if (ObjectUtils.isNotEmpty(syncEmployeePO)){
                offlineCompanyInfoResponseVO.setMerchandiserJobNumber(merchandiserCode);
                offlineCompanyInfoResponseVO.setMerchandiserPost(saleEmployeePO.getUnitName());
                //有公司座机填公司座机
                if (StringUtils.isBlank(syncEmployeePO.getCompanyMobile())){
                    offlineCompanyInfoResponseVO.setMerchandiserMobile(syncEmployeePO.getMobile());
                }else {
                    offlineCompanyInfoResponseVO.setMerchandiserMobile(syncEmployeePO.getCompanyMobile());
                }
                offlineCompanyInfoResponseVO.setMerchandiserName(syncEmployeePO.getEmployeeName());

                //查询QQ和邮箱
                LambdaQueryWrapper<CompanyMerchandiserSalesmanInfoPO> companyMerchandiserSalesmanInfoPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
                companyMerchandiserSalesmanInfoPOLambdaQueryWrapper.eq(CompanyMerchandiserSalesmanInfoPO::getEmployeeCode,merchandiserCode);
                CompanyMerchandiserSalesmanInfoPO companyMerchandiserSalesmanInfoPO = companyMerchandiserSalesmanInfoDAO.selectOne(companyMerchandiserSalesmanInfoPOLambdaQueryWrapper);
                logger.info("companyMerchandiserSalesmanInfoDAO selectOne companyMerchandiserSalesmanInfoPO result is:{}",companyMerchandiserSalesmanInfoPO);
                if (ObjectUtils.isNotEmpty(companyMerchandiserSalesmanInfoPO)){
                    offlineCompanyInfoResponseVO.setMerchandiserEmail(companyMerchandiserSalesmanInfoPO.getEmail());
                    offlineCompanyInfoResponseVO.setMerchandiserQq(companyMerchandiserSalesmanInfoPO.getQq());
                    if (StringUtils.isNotBlank(companyMerchandiserSalesmanInfoPO.getEnterpriseWechatCodeUrl())){
                        offlineCompanyInfoResponseVO.setMerchandiserEnterpriseWechatCodeUrl(fontUrl + companyMerchandiserSalesmanInfoPO.getEnterpriseWechatCodeUrl());
                    }
                }
                //存入redis
                setRedis(offlineCompanyInfoResponseVO,key,companyCode);
            }else {

                //设置业务员信息到跟单员
               copySaleToMerchandiserInfo(offlineCompanyInfoResponseVO,key,companyCode);
            }



        }else {
            offlineCompanyInfoResponseVO = JSONUtil.fromObject(redisService.hget(key, companyCode).toString(), OfflineCompanyInfoResponseVO.class);
            logger.info("get redis offlineCompanyInfoResponseVO result is :{} ",offlineCompanyInfoResponseVO);
        }
        return offlineCompanyInfoResponseVO;
    }

    /**
     * 根据erp编码查询管理员列表
     *
     * @param erpCompanyCodeListRequestVO
     */
    @Override
    public BusinessResponse<List<ApiUserAdminResponse>> getAdminListByErpCompany(ERPCompanyCodeListRequestVO erpCompanyCodeListRequestVO) {
        logger.info("CompanyService queryOfflineCompany parameter getAdminListByErpCompany");
        if (CollectionUtil.isEmpty(erpCompanyCodeListRequestVO.getErpCompanyCodeList())){
            return BusinessResponse.ok(null);
        }
        List<ApiUserAdminResponse> adminListByErpCompany = companyDAO.getAdminListByErpCompany(erpCompanyCodeListRequestVO.getErpCompanyCodeList());
        logger.info("companyDAO getAdminListByErpCompany result is :{}",adminListByErpCompany);
        return BusinessResponse.ok(adminListByErpCompany);
    }

    /**
     * 根据erp编码查询线下企业信息集合
     *
     * @param offlineCompanyCodeListRequestVO
     */
    @Override
    public BusinessResponse<OfflineCompanyInfoByErpCompanyResponseVO> queryOfflineCompanyListByErpCompanyCodeList(OfflineCompanyCodeListRequestVO offlineCompanyCodeListRequestVO) {
        //查询主表

        String erpCompanyCode = offlineCompanyCodeListRequestVO.getErpCompanyCode();
        OfflineCompanyInfoByErpCompanyResponseVO offlineCompanyInfoByErpCompanyResponseVO = new OfflineCompanyInfoByErpCompanyResponseVO();
        LambdaQueryWrapper<UcSyncCustomerInfoPO> ucSyncCustomerInfoPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        ucSyncCustomerInfoPOLambdaQueryWrapper.eq(UcSyncCustomerInfoPO::getCustomerNo,erpCompanyCode);
        UcSyncCustomerInfoPO ucSyncCustomerInfoPO = syncCustomerInfoDao.selectOne(ucSyncCustomerInfoPOLambdaQueryWrapper);
        if (ObjectUtils.isEmpty(ucSyncCustomerInfoPO)){
            return BusinessResponse.fail(ConstantStatusEnum.COMPANY_HAS_BEEN_DISABLED.getCode(),ConstantStatusEnum.COMPANY_HAS_BEEN_DISABLED.getDescCn());
        }
        String platformCode;
        //设置归属地、含税价格属性、以及更新时间
        String ownershipCompany = ucSyncCustomerInfoPO.getOwnershipCompany();
        if (StringUtils.isBlank(ownershipCompany) || StringUtils.equals(ownershipCompany,"DGYHD")){
            //如果为空默认为东莞怡合达
            ownershipCompany = "DGYHD";
            //东莞客户
            platformCode = "010000";
        }else {
            //苏州客户
            platformCode = "050000";
        }
        offlineCompanyInfoByErpCompanyResponseVO.setCompanyName(ucSyncCustomerInfoPO.getCustomerName());
        offlineCompanyInfoByErpCompanyResponseVO.setOwnershipCompany(ownershipCompany);
        offlineCompanyInfoByErpCompanyResponseVO.setTaxPriceAttribute(ucSyncCustomerInfoPO.getTaxPriceAttribute() == null ? com.yhd.common.util.CommonConstant.TWO : ucSyncCustomerInfoPO.getTaxPriceAttribute() );
        offlineCompanyInfoByErpCompanyResponseVO.setTaxPriceAuditDate(ucSyncCustomerInfoPO.getTaxPriceAuditDate());
        offlineCompanyInfoByErpCompanyResponseVO.setCompanyAddress(ucSyncCustomerInfoPO.getRegistryAddress());
        offlineCompanyInfoByErpCompanyResponseVO.setErpCompanyCode(erpCompanyCode);
        offlineCompanyInfoByErpCompanyResponseVO.setCompanyIdentity(platformCode);
        LambdaQueryWrapper<SyncCustomerInfoPrivatePO> syncCustomerInfoPrivatePOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        syncCustomerInfoPrivatePOLambdaQueryWrapper.eq(SyncCustomerInfoPrivatePO::getCompanyCode,platformCode)
                .eq(SyncCustomerInfoPrivatePO::getCustomerNo,erpCompanyCode);
        SyncCustomerInfoPrivatePO syncCustomerInfoPrivatePO = syncCustomerInfoPrivateDao.selectOne(syncCustomerInfoPrivatePOLambdaQueryWrapper);
        logger.info("queryOfflineCompanyListByErpCompanyCodeList syncCustomerInfoPrivateDao selectOne result is :{}",syncCustomerInfoPrivatePO);

        if (ObjectUtils.isEmpty(syncCustomerInfoPrivatePO)){
            //线下客户细表没有数据说明，线下没有这件企业，返回线上数据即可
            return BusinessResponse.ok(offlineCompanyInfoByErpCompanyResponseVO);
        }

        if (com.yhd.common.util.CommonConstant.TWO.equals(syncCustomerInfoPrivatePO.getTransactionStatus())
                || com.yhd.common.util.CommonConstant.ONE.equals(syncCustomerInfoPrivatePO.getExceptionType())){
            //当交易状况为暂停交易或者异常类型为暂停下单则该企业不允许下单，为空的话是默认正常
            offlineCompanyInfoByErpCompanyResponseVO.setTradingStatus(com.yhd.common.util.CommonConstant.TWO);
        }else {
            offlineCompanyInfoByErpCompanyResponseVO.setTradingStatus(com.yhd.common.util.CommonConstant.ONE);
        }


        //如果为空就给她一个4
        Integer discountLevel = syncCustomerInfoPrivatePO.getDiscountLevel();
        if (discountLevel == null || com.yhd.common.util.CommonConstant.ZERO.equals(discountLevel)){
            discountLevel = com.yhd.common.util.CommonConstant.FOUR;
        }
        offlineCompanyInfoByErpCompanyResponseVO.setDiscountLevel(discountLevel);


        //查询月结表
        LambdaQueryWrapper<UcSyncMonthlySettlePO> ucSyncMonthlySettlePOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        ucSyncMonthlySettlePOLambdaQueryWrapper.eq(UcSyncMonthlySettlePO::getCompanyCode,platformCode)
                .eq(UcSyncMonthlySettlePO::getCustomerNo,syncCustomerInfoPrivatePO.getCustomerNo());
        UcSyncMonthlySettlePO ucSyncMonthlySettlePO = ucSyncMonthlySettleDao.selectOne(ucSyncMonthlySettlePOLambdaQueryWrapper);
        logger.info("ucSyncMonthlySettleDao selectOne result is :{}",ucSyncMonthlySettlePO);
        if (ObjectUtils.isNotEmpty(ucSyncMonthlySettlePO)){
            offlineCompanyInfoByErpCompanyResponseVO.setSettlementMethod(ucSyncMonthlySettlePO.getSettlementType());
        }


        //查询业务员是否离职(caller是访客状态)
        String salesNo = syncCustomerInfoPrivatePO.getSalesNo();
        LambdaQueryWrapper<SyncEmployeePO> saleEmployeePOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        saleEmployeePOLambdaQueryWrapper
                .eq(SyncEmployeePO::getEmployeeCode,salesNo)
                .and(ss -> ss.eq(SyncEmployeePO::getStatus,CommonConstant.NORMAL).or().eq(SyncEmployeePO::getStatus,"caller"));
        SyncEmployeePO saleEmployeePO = syncEmployeeDAO.selectOne(saleEmployeePOLambdaQueryWrapper);
        logger.info("syncEmployeeDAO selectOne saleEmployeePO is :{}",saleEmployeePO);
        if (ObjectUtils.isNotEmpty(saleEmployeePO)) {
            //查询线下业务员跟单员表
            List<MerchandiserResponseVO> merchandiserList = getMerchandiserList(salesNo, platformCode);
            offlineCompanyInfoByErpCompanyResponseVO.setMerchandiserResponseVOList(merchandiserList);

            //设置业务员信息
            offlineCompanyInfoByErpCompanyResponseVO.setOperatorJobNumber(saleEmployeePO.getEmployeeCode());
            offlineCompanyInfoByErpCompanyResponseVO.setOperatorName(saleEmployeePO.getEmployeeName());
            offlineCompanyInfoByErpCompanyResponseVO.setOperatorPost(saleEmployeePO.getUnitName());
            //有公司座机填公司座机
            if (StringUtils.isBlank(saleEmployeePO.getCompanyMobile())) {
                offlineCompanyInfoByErpCompanyResponseVO.setOperatorMobile(saleEmployeePO.getMobile());
            } else {
                offlineCompanyInfoByErpCompanyResponseVO.setOperatorMobile(saleEmployeePO.getCompanyMobile());

            }

            //查询企业是否在线上注册过
            LambdaQueryWrapper<CompanyPO> companyPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            companyPOLambdaQueryWrapper.eq(CompanyPO::getErpCompanyCode,erpCompanyCode);
            CompanyPO companyPO = companyDAO.selectOne(companyPOLambdaQueryWrapper);
            logger.info("companyDAO selectOne result is :{}",companyPO);

            if (ObjectUtils.isEmpty(companyPO)){
                //线上没有直接返回
                return BusinessResponse.ok(offlineCompanyInfoByErpCompanyResponseVO);
            }


            //查询业务员QQ和邮箱
            LambdaQueryWrapper<CompanyMerchandiserSalesmanInfoPO> salesmanInfoPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            salesmanInfoPOLambdaQueryWrapper.eq(CompanyMerchandiserSalesmanInfoPO::getEmployeeCode, salesNo);
            CompanyMerchandiserSalesmanInfoPO salesmanInfoPO = companyMerchandiserSalesmanInfoDAO.selectOne(salesmanInfoPOLambdaQueryWrapper);
            logger.info("companyMerchandiserSalesmanInfoDAO selectOne salesmanInfoPO result is:{}", salesmanInfoPO);
            if (ObjectUtils.isNotEmpty(salesmanInfoPO)) {
                offlineCompanyInfoByErpCompanyResponseVO.setOperatorEmail(salesmanInfoPO.getEmail());
                offlineCompanyInfoByErpCompanyResponseVO.setOperatorQq(salesmanInfoPO.getQq());
            }


            //查询FA是否有设置的专属跟单
            LambdaQueryWrapper<CompanyMerchandiserPO> companyMerchandiserPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            companyMerchandiserPOLambdaQueryWrapper.eq(CompanyMerchandiserPO::getPlatformCode,"YHD-fa")
                    .eq(CompanyMerchandiserPO::getCompanyCode, companyPO.getCompanyCode()).eq(CompanyMerchandiserPO::getSalesmanCode, salesNo);
            CompanyMerchandiserPO companyMerchandiserPO = companyMerchandiserDAO.selectOne(companyMerchandiserPOLambdaQueryWrapper);
            logger.info("companyMerchandiserDAO selectOne companyMerchandiserPO is :{}", companyMerchandiserPO);
            if (ObjectUtils.isEmpty(companyMerchandiserPO)) {
                return BusinessResponse.ok(offlineCompanyInfoByErpCompanyResponseVO);
            }

            String merchandiserCode = companyMerchandiserPO.getMerchandiserCode();
            //查看业务员跟单员线下表是否有关联
            LambdaQueryWrapper<SyncCorrespondingMerchandiserPO> syncCorrespondingMerchandiserPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            syncCorrespondingMerchandiserPOLambdaQueryWrapper.eq(SyncCorrespondingMerchandiserPO::getEmpNo, merchandiserCode)
                    .eq(SyncCorrespondingMerchandiserPO::getSalesNo, salesNo);

            Long count = syncCorrespondingMerchandiserDao.selectCount(syncCorrespondingMerchandiserPOLambdaQueryWrapper);
            logger.info("syncCorrespondingMerchandiserDao selectCount count result is:{}",count);
            if (count.intValue() == com.yhd.common.util.CommonConstant.ZERO) {
                //为空直接返回
                return BusinessResponse.ok(offlineCompanyInfoByErpCompanyResponseVO);
            }

            //查看员工是否离职
            LambdaQueryWrapper<SyncEmployeePO> merchandiserEmployeePOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            merchandiserEmployeePOLambdaQueryWrapper.eq(SyncEmployeePO::getEmployeeCode, merchandiserCode)
                    .and(ss -> ss.eq(SyncEmployeePO::getStatus, CommonConstant.NORMAL).or().eq(SyncEmployeePO::getStatus, "caller"));
            SyncEmployeePO syncEmployeePO = syncEmployeeDAO.selectOne(merchandiserEmployeePOLambdaQueryWrapper);
            logger.info("syncEmployeeDAO selectOne syncEmployeePO is :{}", companyMerchandiserPO);
            if (ObjectUtils.isNotEmpty(syncEmployeePO)) {
                offlineCompanyInfoByErpCompanyResponseVO.setMerchandiserJobNumber(merchandiserCode);
                offlineCompanyInfoByErpCompanyResponseVO.setMerchandiserPost(saleEmployeePO.getUnitName());
                //有公司座机填公司座机
                offlineCompanyInfoByErpCompanyResponseVO.setMerchandiserMobile(StringUtils.isBlank(syncEmployeePO.getCompanyMobile())
                        ? syncEmployeePO.getMobile() : syncEmployeePO.getCompanyMobile());
                offlineCompanyInfoByErpCompanyResponseVO.setMerchandiserName(syncEmployeePO.getEmployeeName());

                //查询QQ和邮箱
                LambdaQueryWrapper<CompanyMerchandiserSalesmanInfoPO> companyMerchandiserSalesmanInfoPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
                companyMerchandiserSalesmanInfoPOLambdaQueryWrapper.eq(CompanyMerchandiserSalesmanInfoPO::getEmployeeCode, merchandiserCode);
                CompanyMerchandiserSalesmanInfoPO companyMerchandiserSalesmanInfoPO = companyMerchandiserSalesmanInfoDAO.selectOne(companyMerchandiserSalesmanInfoPOLambdaQueryWrapper);
                logger.info("companyMerchandiserSalesmanInfoDAO selectOne companyMerchandiserSalesmanInfoPO result is:{}", companyMerchandiserSalesmanInfoPO);
                if (ObjectUtils.isNotEmpty(companyMerchandiserSalesmanInfoPO)) {
                    offlineCompanyInfoByErpCompanyResponseVO.setMerchandiserEmail(companyMerchandiserSalesmanInfoPO.getEmail());
                    offlineCompanyInfoByErpCompanyResponseVO.setMerchandiserQq(companyMerchandiserSalesmanInfoPO.getQq());
                }
            }
        }
        return BusinessResponse.ok(offlineCompanyInfoByErpCompanyResponseVO);
    }


    /**
     * 存入redis
     * @param offlineCompanyInfoResponseVO 线下企业参数
     * @param key redisKey
     * @param companyCode 企业编码
     * */
    private void setRedis(OfflineCompanyInfoResponseVO offlineCompanyInfoResponseVO,String key,String companyCode){
        String json = JSONUtil.toJson(offlineCompanyInfoResponseVO);
        logger.info("get json offlineCompanyInfoResponseVO is :{}",json);
        Boolean result = redisService.hset(key, companyCode, json);
        logger.info("redisService hset offlineCompanyInfoResponseVOJson result is :{}",result);
    }



    /**
     * 设置业务员信息给跟单员
     * @param offlineCompanyInfoResponseVO 线下企业参数
     * @param key redisKey
     * @param companyCode 企业编码
     * */
    private void copySaleToMerchandiserInfo(OfflineCompanyInfoResponseVO offlineCompanyInfoResponseVO, String key, String companyCode){
        offlineCompanyInfoResponseVO.setMerchandiserName(offlineCompanyInfoResponseVO.getOperatorName());
        offlineCompanyInfoResponseVO.setMerchandiserJobNumber(offlineCompanyInfoResponseVO.getOperatorJobNumber());
        offlineCompanyInfoResponseVO.setMerchandiserEmail(offlineCompanyInfoResponseVO.getOperatorEmail());
        offlineCompanyInfoResponseVO.setMerchandiserQq(offlineCompanyInfoResponseVO.getOperatorQq());
        offlineCompanyInfoResponseVO.setMerchandiserMobile(offlineCompanyInfoResponseVO.getOperatorMobile());
        offlineCompanyInfoResponseVO.setMerchandiserEnterpriseWechatCodeUrl(offlineCompanyInfoResponseVO.getOperatorEnterpriseWechatCodeUrl());

        //存入redis
        setRedis(offlineCompanyInfoResponseVO,key,companyCode);
    }



    /**
     * 根据业务员工号查询跟单员集合
     * @param operatorCode 业务员工号
     * @param platformCode 平台
     * */
    private List<MerchandiserResponseVO> getMerchandiserList(String operatorCode, String platformCode){
        List<MerchandiserResponseVO> merchandiserResponseVOList = new ArrayList<>();
        LambdaQueryWrapper<SyncCorrespondingMerchandiserPO> syncCorrespondingMerchandiserPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        syncCorrespondingMerchandiserPOLambdaQueryWrapper.eq(SyncCorrespondingMerchandiserPO::getSalesNo,operatorCode)
                .eq(SyncCorrespondingMerchandiserPO::getCompanyCode,platformCode);
        List<SyncCorrespondingMerchandiserPO> syncCorrespondingMerchandiserPOList = syncCorrespondingMerchandiserDao.selectList(syncCorrespondingMerchandiserPOLambdaQueryWrapper);
        logger.info("syncCorrespondingMerchandiserDao selectList result count is:{}",syncCorrespondingMerchandiserPOList.size());
        if (!syncCorrespondingMerchandiserPOList.isEmpty()){
            List<String> merchandiserJobNumberList = syncCorrespondingMerchandiserPOList.stream().map(SyncCorrespondingMerchandiserPO::getEmpNo).collect(Collectors.toList());
            logger.info("merchandiserJobNumberList is :{}",merchandiserJobNumberList.toString());
            LambdaQueryWrapper<SyncEmployeePO> syncEmployeePOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            syncEmployeePOLambdaQueryWrapper.in(SyncEmployeePO::getEmployeeCode,merchandiserJobNumberList)
                    .and(ss -> ss.eq(SyncEmployeePO::getStatus,CommonConstant.NORMAL).or().eq(SyncEmployeePO::getStatus,"caller"));
            List<SyncEmployeePO> syncEmployeePOList = syncEmployeeDAO.selectList(syncEmployeePOLambdaQueryWrapper);
            logger.info("syncEmployeeDAO selectList result count is:{}",syncEmployeePOList.size());
            if (!syncEmployeePOList.isEmpty()){
                merchandiserResponseVOList = syncEmployeePOList.stream().map(e -> {
                    MerchandiserResponseVO merchandiserResponseVO = new MerchandiserResponseVO();
                    merchandiserResponseVO.setMerchandiserName(e.getEmployeeName());
                    merchandiserResponseVO.setMerchandiserJobNumber(e.getEmployeeCode());
                    //有公司联系电话取公司联系电话
                    merchandiserResponseVO.setMerchandiserMobile(StringUtils.isBlank(e.getCompanyMobile()) ? e.getMobile() : e.getCompanyMobile());
                    return merchandiserResponseVO;
                }).collect(Collectors.toList());
            }
        }

        return merchandiserResponseVOList;
    }
}
