package com.yhd.buc.foreign.service.api;

import com.yhd.common.pojo.vo.BusinessResponse;

/**
 * <AUTHOR>
 * @version Id:DeleteRedisKeyService.java, v 0.12023/4/3 10:52 kangrong Exp $
 */
public interface DeleteRedisKeyService {

    /**
     * @param erpCompanyCode ERP企业标识
     * @param subsidiaryCode 子公司编码
     * */
    BusinessResponse<String> deleteOfflineCompanyKey(String erpCompanyCode,String subsidiaryCode);

    /**
     * @param account 账号
     * 删除账号缓存
     * */
    BusinessResponse<String> deleteUserInfo(String account);
}
