package com.yhd.buc.foreign.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yhd.buc.foreign.constant.CommonConstant;
import com.yhd.buc.foreign.dao.AccountDAO;
import com.yhd.buc.foreign.dao.CompanyDAO;
import com.yhd.buc.foreign.dao.CompanyRelationUserDAO;
import com.yhd.buc.foreign.dao.PlatformDao;
import com.yhd.buc.foreign.enums.ConstantStatusEnum;
import com.yhd.buc.foreign.pojo.po.AccountPO;
import com.yhd.buc.foreign.pojo.po.CompanyPO;
import com.yhd.buc.foreign.pojo.po.CompanyRelationUserPO;
import com.yhd.buc.foreign.service.api.DeleteRedisKeyService;
import com.yhd.buc.foreign.utils.RedisUserUtils;
import com.yhd.common.exception.BizException;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id:DeleteRedisKeyServiceImpl.java, v 0.12023/4/3 10:57 kangrong Exp $
 */
@Service
public class DeleteRedisKeyServiceImpl implements DeleteRedisKeyService {
    private static final Logger logger = LogUtils.getLogger();

    @Resource
    private CompanyDAO companyDAO;

    @Resource
    private PlatformDao platformDao;

    @Resource
    private AccountDAO accountDAO;

    @Resource
    private RedisUserUtils redisUserUtils;

    @Resource
    private CompanyRelationUserDAO companyRelationUserDAO;



    /**
     * 删除线下企业缓存
     * @param erpCompanyCode ERP企业标识
     * @param subsidiaryCode 子公司编码
     */
    @Override
    public BusinessResponse<String> deleteOfflineCompanyKey(String erpCompanyCode, String subsidiaryCode) {
        logger.info("visit  DeleteRedisKeyService deleteOfflineCompanyKey");
        //查询企业
        LambdaQueryWrapper<CompanyPO> companyPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        companyPOLambdaQueryWrapper.eq(CompanyPO::getErpCompanyCode,erpCompanyCode).eq(CompanyPO::getStatus, CommonConstant.ENABLED);
        CompanyPO companyPO = companyDAO.selectOne(companyPOLambdaQueryWrapper);
        logger.info("deleteOfflineCompanyKey companyDAO selectOne result is :{}",companyPO);
        if (ObjectUtils.isEmpty(companyPO)){
            throw new BizException(ConstantStatusEnum.COMPANY_HAS_BEEN_DISABLED.getCode(), ConstantStatusEnum.COMPANY_HAS_BEEN_DISABLED.getDescCn());
        }
        String companyCode = companyPO.getCompanyCode();
        //查询关联平台
        logger.info("platformDao getPlatformInfoCodeListBySubsidiaryCode parameter is :{}",subsidiaryCode);
        List<String> platformCodeList = platformDao.getPlatformInfoCodeListBySubsidiaryCode(subsidiaryCode);
        logger.info("platformDao getPlatformInfoCodeListBySubsidiaryCode result is :{}",platformCodeList);
        if (platformCodeList.isEmpty()){
            return BusinessResponse.ok(null);
        }
        //删除线下企业缓存
        platformCodeList.forEach(e ->redisUserUtils.deleteRedisOfflineCompanyKey(companyCode,e));

        //删除线上企业缓存
        redisUserUtils.deleteRedisCompanyKey(companyCode);

        //查询用户关联信息
        List<String> userCodeList = companyRelationUserDAO.getUserCodeListByCompanyCode(companyCode);
        if (userCodeList.isEmpty()){
            return BusinessResponse.ok(CommonConstant.SUCCESS);
        }
        //删除线上用户信息缓存
        userCodeList.forEach(e -> redisUserUtils.deleteRedisUserInfo(e));
        return BusinessResponse.ok(CommonConstant.SUCCESS);
    }

    /**
     * @param account 账号
     *                删除账号缓存
     */
    @Override
    public BusinessResponse<String> deleteUserInfo(String account) {
        logger.info("DeleteRedisKeyService deleteUserInfo ");
        LambdaQueryWrapper<AccountPO> accountPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        accountPOLambdaQueryWrapper.eq(AccountPO::getAccount,account);
        AccountPO accountPO = accountDAO.selectOne(accountPOLambdaQueryWrapper);
        logger.info("accountDAO selectOne is :{}",accountPO);
        String userCode = accountPO.getUserCode();

        //删除角色缓存
        redisUserUtils.deleteRedisUserRole(accountPO.getUserCode());

        //删除权限
        redisUserUtils.deleteRedisUserControl(accountPO.getUserCode());

        LambdaQueryWrapper<CompanyRelationUserPO> companyRelationUserPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        companyRelationUserPOLambdaQueryWrapper.eq(CompanyRelationUserPO::getUserCode,userCode);

        CompanyRelationUserPO companyRelationUserPO = companyRelationUserDAO.selectOne(companyRelationUserPOLambdaQueryWrapper);
        if (ObjectUtils.isNotEmpty(companyRelationUserPO)){
            //删除线上线下企业缓存
            String companyCode = companyRelationUserPO.getCompanyCode();
            redisUserUtils.deleteRedisOfflineCompanyKey(companyCode);
            redisUserUtils.deleteRedisCompanyKey(companyCode);
        }

        //删除用户缓存
        redisUserUtils.deleteRedisUserInfo(userCode);
        return BusinessResponse.ok(CommonConstant.SUCCESS);
    }
}
