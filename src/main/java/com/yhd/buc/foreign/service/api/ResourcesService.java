package com.yhd.buc.foreign.service.api;

import com.yhd.buc.foreign.pojo.vo.response.ResourcesResponseVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id:ResourcesService.java, v 0.12023/3/22 11:40 kangrong Exp $
 */
public interface ResourcesService {

    /**
     * 获取根据平台编码所有资源
     * @param platformCode 平台编码
     * */

    List<ResourcesResponseVO> getResourcesListByPlatform(String platformCode);
}
