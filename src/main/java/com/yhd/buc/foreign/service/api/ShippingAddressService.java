package com.yhd.buc.foreign.service.api;

import com.github.pagehelper.PageInfo;
import com.yhd.buc.foreign.pojo.vo.request.*;
import com.yhd.buc.foreign.pojo.vo.response.UserAddressResponseVO;
import com.yhd.common.pojo.vo.BusinessResponse;

import java.util.List;


/**
 * <AUTHOR>
 * @version Id:ShippingAddressService.java, v 0.12022/6/20 14:24 kangrong Exp $
 */
public interface ShippingAddressService {
    /**
     * 添加地址
     * */
    BusinessResponse<String> addUserAddress(UserAddressRequestVO userAddressRequestVO);

    /**
     * 删除地址
     * */
    BusinessResponse<String> deleteUserAddress(UserAddressCodeRequestVO userAddressCodeRequestVO);

    /**
     * 更新地址
     * */
    BusinessResponse<String> updateUserAddress(UserAddressRequestVO userAddressRequestVO);


    /**
     * 查询地址集合
     * */
    BusinessResponse<PageInfo<UserAddressResponseVO>> queryUserAddress(UserCodePageRequestVO userCodePageRequestVO);

    /**
     * 查询剩余多少新增收货地址的数量
     * */
    BusinessResponse<Integer>  queryUserAddressNum(AddressTypeRequestVO addressTypeRequestVO);

    /**
     * 设置默认地址
     * */
    BusinessResponse<String> setDefaultAddress(UserAddressCodeRequestVO userAddressCodeRequestVO);

    /**
     * 获取地址详情
     * */
    BusinessResponse<UserAddressResponseVO> getUserAddressDerails(UserAddressCodeRequestVO userAddressCodeRequestVO);


    /**
     * 管理员收货地址集合
     * @param companyCode 企业编码
     * */
    BusinessResponse<List<UserAddressResponseVO>> getCompanyAdminAddressList(String companyCode);
}
