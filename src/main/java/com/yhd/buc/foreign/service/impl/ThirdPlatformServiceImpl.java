package com.yhd.buc.foreign.service.impl;

import cn.hutool.crypto.digest.DigestUtil;
import com.yhd.buc.foreign.constant.CommonConstant;
import com.yhd.buc.foreign.constant.UrlConstant;
import com.yhd.buc.foreign.enums.ConstantStatusEnum;
import com.yhd.buc.foreign.pojo.vo.request.*;
import com.yhd.buc.foreign.sao.client.ThirdPartyQueryCompanySAO;
import com.yhd.buc.foreign.service.api.ThirdPlatformService;
import com.yhd.common.exception.BizException;
import com.yhd.common.json.JSONUtil;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id:thirdPlatformServiceImpl.java, v 0.12022/7/4 15:19 kangrong Exp $
 */
@Service
@RefreshScope
public class ThirdPlatformServiceImpl implements ThirdPlatformService {
    /**
     * 日志参数
     */
    private static final Logger logger = LogUtils.getLogger();
    @Value("${third-platform.QCC_app_key}")
    private String qccAppKey;

    @Value("${third-platform.QCC_SECRET_KEY}")
    private String qccSecretKey;

    @Value("${third-platform.QXB_app_key}")
    private String qxbAppKey;

    @Value("${third-platform.QXB_SECRET_KEY}")
    private String qxbSecretKey;

    @Value("${third-platform.type}")
    private String thirdPlatformType;

    @Autowired
    private ThirdPartyQueryCompanySAO thirdPartyQueryCompanySAO;


    /**
     * 企查查企业查询接口
     *
     * @param thirdPlatformQueryCompanyVO 企查查接收VO
     */
    @Override
    public BusinessResponse<Object> thirdPartyQueryCompany(ThirdQueryCompanyRequestVO thirdPlatformQueryCompanyVO) {
        logger.info("enjoy ThirdPlatformService thirdPartyQueryCompany");
                Object result = null;
                String companyName = thirdPlatformQueryCompanyVO.getCompanyName();
                String type = thirdPlatformQueryCompanyVO.getType();
                if (companyName.length() < 3){
                    logger.info("thirdPlatformQueryCompanyVO  getCompanyName  length  lt 3:{}",companyName);
                    return BusinessResponse.ok(null);
                }

                //企信宝
                if (StringUtils.equals(thirdPlatformType,"qxb")){
                    result = qxbQueryCompany(companyName, type);
                }else {
                    //企查查
                    result = qccQueryCompany(companyName, type);
                }
                    return BusinessResponse.ok(result);


    }

    /**
     * 启信宝查询企业
     * */
    private Object qxbQueryCompany(String companyName, String type){
        if (CommonConstant.QCC_PRECISE_QUERY.equals(type)){
            //启信宝精确查询
            //获取查询url
            String qxbPreciseQueryULR = null;
            qxbPreciseQueryULR = UrlConstant.QXB_PRECISE_QUERY + qxbAppKey + UrlConstant.QXB_SECRET_KEY + qxbSecretKey + UrlConstant.QCC_KEYWORD + companyName;
            logger.info("get qxbPreciseQueryULR is :{}",qxbPreciseQueryULR);
            String qxbPreciseQueryResult = thirdPartyQueryCompanySAO.qxbPreciseQuery(qxbPreciseQueryULR);
            logger.info("get qxbPreciseQueryULR result is :{}",qxbPreciseQueryResult);
            QXBPreciseRequestVO qxbPreciseRequestVO = JSONUtil.fromObject(qxbPreciseQueryResult, QXBPreciseRequestVO.class);
            logger.info("get QXBPreciseRequestVO is :{}",qxbPreciseRequestVO);
            //判断返回码是否错误
            if (!CommonConstant.SUCCESS_STATUS_CODE.equals(qxbPreciseRequestVO.getStatus())||StringUtils.isBlank(qxbPreciseRequestVO.getStatus())){
                logger.error("QXB qxbPreciseSearchVO error :{}",qxbPreciseRequestVO);
                throw new BizException(ConstantStatusEnum.DATA_NOT_EXIST.getCode(), qxbPreciseRequestVO.getMessage());
            }
            //判断查询成功时，企查查是否返回空值
            if (ObjectUtils.isEmpty(qxbPreciseRequestVO)){
                logger.error("QXB qxbPreciseSearchVO is null :{}",qxbPreciseRequestVO);
                throw new BizException(ConstantStatusEnum.DATA_NOT_EXIST.getCode(), ConstantStatusEnum.DATA_NOT_EXIST.getDescCn());
            }
            return qxbPreciseRequestVO;
        }else if (CommonConstant.QCC_FUZZY_QUERY.equals(type)){
            //模糊查询
            return qxbFuzzyQuery(companyName);
        }
       else {
            logger.error("QccQuery type error ：{}",type);
            throw new BizException(ConstantStatusEnum.PARAM_VALIDATION_ERROR.getCode(), ConstantStatusEnum.PARAM_VALIDATION_ERROR.getDescCn());
        }
    }

    /**
     * 企查查查询企业
     * */
    private Object qccQueryCompany(String companyName, String type){
        //拼装token
        String token =null;
        //获取当时时间戳
        Long second = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        String nowTime = String.valueOf(second);
        token = DigestUtil.md5Hex(qccAppKey + nowTime + qccSecretKey).toUpperCase();
        logger.info("get  QCC token is :{} ",token);
        //模糊查询
        if (CommonConstant.QCC_FUZZY_QUERY.equals(type)){
            String qccFuzzyQueryUrl =null;
            //获取模糊查询URL
            qccFuzzyQueryUrl = UrlConstant.QCC_FUZZY_QUERY + qccAppKey +  UrlConstant.QCC_SEARCH_KEYWORDS + companyName;
            logger.info("get QCCQueryURL parameter is :{} ",qccFuzzyQueryUrl);
            String qccFuzzyQueryResult = null;
            QCCFuzzySearchRequestVO qccFuzzySearchRequestVO = null;
            try {
                qccFuzzyQueryResult = thirdPartyQueryCompanySAO.qccFuzzyQuery(qccFuzzyQueryUrl,token,nowTime);
                logger.info("get QCCQuery result is :{}", qccFuzzyQueryResult);
                qccFuzzySearchRequestVO = JSONUtil.fromObject(qccFuzzyQueryResult, QCCFuzzySearchRequestVO.class);
                logger.info("get QCCFuzzySearchRequestVO is :{}",qccFuzzySearchRequestVO);
            } catch (Exception e) {
                logger.error("qcc fuzzy query error");
                throw new BizException(ConstantStatusEnum.QCC_FUZZY_ERROR.getCode(), ConstantStatusEnum.QCC_FUZZY_ERROR.getDescCn());
            }
            if (StringUtils.isBlank(qccFuzzySearchRequestVO.getStatus())){
                logger.error("qcc fuzzy query error");
                throw new BizException(ConstantStatusEnum.QCC_FUZZY_ERROR.getCode(), ConstantStatusEnum.QCC_FUZZY_ERROR.getDescCn());
            }
            //判断返回码是否错误
            if (!CommonConstant.SUCCESS_STATUS_CODE.equals(qccFuzzySearchRequestVO.getStatus())){
                logger.error("QCC QccFuzzyQuery error :{}",qccFuzzyQueryResult);
                throw new BizException(ConstantStatusEnum.DATA_NOT_EXIST.getCode(), ConstantStatusEnum.DATA_NOT_EXIST.getDescCn());
            }
            //判断查询成功时，企查查是否返回空值
            if (ObjectUtils.isEmpty(qccFuzzySearchRequestVO)){
                logger.error("QCC fuzzy query is null :{}",qccFuzzyQueryResult);
                throw new BizException(ConstantStatusEnum.DATA_NOT_EXIST.getCode(), ConstantStatusEnum.DATA_NOT_EXIST.getDescCn());
            }
            List<String> companyList = qccFuzzySearchRequestVO.getResult().stream()
                    .map(com.yhd.buc.foreign.pojo.vo.request.QCCFuzzySearchRequestVO.Enterprise::getName).collect(Collectors.toList());
            logger.info("companyList is :{}",companyList);
            return companyList;

        }
        //精确查询
        else if (CommonConstant.QCC_PRECISE_QUERY.equals(type)){
            String qccPreciseQueryUrl = null;
            qccPreciseQueryUrl = UrlConstant.QCC_PRECISE_QUERY + qccAppKey + UrlConstant.QCC_KEYWORD + companyName;
            logger.info("get qccPreciseQueryURL is :{} ",qccPreciseQueryUrl);
            String qccPreciseQueryResult = thirdPartyQueryCompanySAO.qccPreciseQuery(qccPreciseQueryUrl,token,nowTime);
            QCCPreciseQueryRequestVO qccPreciseQueryRequestVO = JSONUtil.fromObject(qccPreciseQueryResult, QCCPreciseQueryRequestVO.class);
            logger.info("get QCCPreciseQueryRequestVO result is :{}", qccPreciseQueryRequestVO);
            //判断返回码
            if (!CommonConstant.SUCCESS_STATUS_CODE.equals(qccPreciseQueryRequestVO.getStatus())){
                logger.error("QCC QccPreciseQuery error :{}",qccPreciseQueryResult);
               throw new BizException(ConstantStatusEnum.DATA_NOT_EXIST.getCode(), ConstantStatusEnum.DATA_NOT_EXIST.getDescCn());
            }
            //判断查询成功时，企查查是否返回空值
            if (ObjectUtils.isEmpty(qccPreciseQueryRequestVO)){
                logger.error("QCC precise query is null :{}",qccPreciseQueryResult);
                throw new BizException(ConstantStatusEnum.DATA_NOT_EXIST.getCode(), ConstantStatusEnum.DATA_NOT_EXIST.getDescCn());
            }
            return qccPreciseQueryRequestVO;

        }else {
            logger.error("QccQuery type error ：{}",type);
            throw new BizException(ConstantStatusEnum.PARAM_VALIDATION_ERROR.getCode(), ConstantStatusEnum.PARAM_VALIDATION_ERROR.getDescCn());
        }
    }

    /**
     * 启信宝模糊查询
     *
     * */
    private List<String> qxbFuzzyQuery(String companyName){
        String qxbFuzzyQueryULR;
        qxbFuzzyQueryULR = UrlConstant.QXB_FUZZY_QUERY + qxbAppKey + UrlConstant.QXB_SECRET_KEY + qxbSecretKey + UrlConstant.QCC_KEYWORD +companyName;
        logger.info("get qxbFuzzyQueryULR is :{}",qxbFuzzyQueryULR);
        QXBFuzzySearchRequestVO qxbFuzzySearchRequestVO = null;
        try {
            String qxbFuzzyQueryResult = thirdPartyQueryCompanySAO.qxbFuzzyQuery(qxbFuzzyQueryULR);
            logger.info("get qxbFuzzyQueryResult result is :{}",qxbFuzzyQueryResult);
            qxbFuzzySearchRequestVO = cn.hutool.json.JSONUtil.toBean(qxbFuzzyQueryResult, QXBFuzzySearchRequestVO.class);
            logger.info("get QXBFuzzySearchRequestVO is :{}",qxbFuzzySearchRequestVO);
        } catch (Exception e) {
            logger.error("qxb fuzzy query error is :{}",e.toString());
            throw new BizException(ConstantStatusEnum.QXB_FUZZY_ERROR.getCode(), ConstantStatusEnum.QXB_FUZZY_ERROR.getDescCn());
        }
        //若状态为空，说明启信宝查询有问题
        if (StringUtils.isBlank(qxbFuzzySearchRequestVO.getStatus())){
            logger.error("qxb fuzzy query error");
            throw new BizException(ConstantStatusEnum.QXB_FUZZY_ERROR.getCode(), ConstantStatusEnum.QXB_FUZZY_ERROR.getDescCn());
        }
        //判断返回码是否错误
        if (!CommonConstant.SUCCESS_STATUS_CODE.equals(qxbFuzzySearchRequestVO.getStatus())){
            logger.error("QXB QXBFuzzySearchRequestVO error :{}",qxbFuzzySearchRequestVO);
            throw new BizException(ConstantStatusEnum.DATA_NOT_EXIST.getCode(), qxbFuzzySearchRequestVO.getMessage());
        }
        //判断查询成功时，企查查是否返回空值
        if (ObjectUtils.isEmpty(qxbFuzzySearchRequestVO)){
            logger.error("QXB fuzzy query is null :{}",qxbFuzzySearchRequestVO);
            throw new BizException(ConstantStatusEnum.DATA_NOT_EXIST.getCode(), ConstantStatusEnum.DATA_NOT_EXIST.getDescCn());
        }
        List<String> companyList = qxbFuzzySearchRequestVO.getData().getItems().stream()
                .map(com.yhd.buc.foreign.pojo.vo.request.QXBFuzzySearchRequestVO.EnterprisePage.Enterprise::getName).collect(Collectors.toList());
        logger.info("companyList is :{}",companyList);
        return companyList;
    }
}
