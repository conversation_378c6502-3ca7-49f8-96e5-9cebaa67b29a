package com.yhd.buc.foreign.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.yhd.buc.foreign.common.BusinessResponseCommon;
import com.yhd.buc.foreign.constant.CommonConstant;
import com.yhd.buc.foreign.dao.CompanyAdministratorDao;
import com.yhd.buc.foreign.dao.ShippingAddressDao;
import com.yhd.buc.foreign.enums.ConstantStatusEnum;
import com.yhd.buc.foreign.pojo.po.CompanyAdministratorPO;
import com.yhd.buc.foreign.pojo.po.ShippingAddressPO;
import com.yhd.buc.foreign.pojo.vo.request.*;
import com.yhd.buc.foreign.pojo.vo.response.UserAddressResponseVO;
import com.yhd.buc.foreign.service.api.ShippingAddressService;
import com.yhd.buc.foreign.utils.BaseUtil;
import com.yhd.buc.foreign.utils.PageUtil;
import com.yhd.common.exception.BizException;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.common.util.UUIDUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id:ShippingAddressServiceImpl.java, v 0.12023/3/9 15:22 kangrong Exp $
 */
@Service
public class ShippingAddressServiceImpl implements ShippingAddressService {

    private static final Logger logger = LogUtils.getLogger();

    @Resource
    private ShippingAddressDao shippingAddressDao;


    @Resource
    private CompanyAdministratorDao companyAdministratorDao;

    /**
     * 添加地址
     *
     * @param userAddressRequestVO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BusinessResponse<String> addUserAddress(UserAddressRequestVO userAddressRequestVO) {
        logger.info("visit ShippingAddressService addUserAddress");
        String userCode = userAddressRequestVO.getUserCode();
        String defaultStatus = userAddressRequestVO.getDefaultStatus();
        String type = userAddressRequestVO.getType();
       String contactNumber = userAddressRequestVO.getContactNumber();
        Pattern pattern = Pattern.compile(CommonConstant.MOBILE_NUMBER_REGULAR_MATCH_TEMPLATE);
        if (!pattern.matcher(contactNumber).matches()) {
            logger.error("mobile number format error :{}",contactNumber);
            throw new BizException(ConstantStatusEnum.MOBILE_NUMBER_FORMAT_ERROR.getCode(),ConstantStatusEnum.MOBILE_NUMBER_FORMAT_ERROR.getDescCn());
        }
        if (StringUtils.isBlank(type)){
            //不传默认为收货地址
            type = CommonConstant.SHIPPING;
        }
        int count = queryCreateAddressNum(userCode,type);
        //判断地址是否超出
        if (StringUtils.equals(CommonConstant.SHIPPING,type)){
            //收货地址
            if (CommonConstant.MAX_SHIPPING_ADDRESS_NUMBER <= count) {
                logger.error("address number out of limit");
                return BusinessResponseCommon.fail(ConstantStatusEnum.ADDRESS_NUMBER_OUT_OF_LIMIT.getCode(),
                        ConstantStatusEnum.ADDRESS_NUMBER_OUT_OF_LIMIT.getDescCn());
            }
        }else if (StringUtils.equals(CommonConstant.INVOICE,type)){
            //发票地址
            if (CommonConstant.MAX_INVOICE_ADDRESS_NUMBER <= count) {
                logger.error("address number out of limit");
                return BusinessResponseCommon.fail(ConstantStatusEnum.ADDRESS_NUMBER_OUT_OF_LIMIT.getCode(),
                        ConstantStatusEnum.ADDRESS_NUMBER_OUT_OF_LIMIT.getDescCn());
            }
        }else {
            return BusinessResponseCommon.fail(ConstantStatusEnum.PARAM_VALIDATION_ERROR.getCode(),
                    ConstantStatusEnum.PARAM_VALIDATION_ERROR.getDescCn());
        }

        //如果是要设置新的默认地址
        if (StringUtils.equals(CommonConstant.TRUE,defaultStatus)){
            judgmentDefault(userCode,type);
        }
        ShippingAddressPO shippingAddressPO = new ShippingAddressPO();
        BeanUtils.copyProperties(userAddressRequestVO,shippingAddressPO);
        shippingAddressPO.setType(type);
        shippingAddressPO.setStatus(CommonConstant.ENABLED);
        String id = UUIDUtils.getStringUUID();
        shippingAddressPO.setId(id);
        BaseUtil.setCreatedParams(shippingAddressPO);
        logger.info("shippingAddressDao insert parameter is :{}",shippingAddressPO);
        int insertResult = shippingAddressDao.insert(shippingAddressPO);
        logger.info("shippingAddressDao insert result is :{}",insertResult);
        if (insertResult == com.yhd.common.util.CommonConstant.ZERO){
            throw new BizException(ConstantStatusEnum.DATABASE_ADD_EXCEPTION.getCode(), ConstantStatusEnum.DATABASE_ADD_EXCEPTION.getDescCn());
        }
        return BusinessResponse.ok(id);
    }

    /**
     * 删除地址
     *
     * @param userAddressCodeRequestVO
     */
    @Override
    public BusinessResponse<String> deleteUserAddress(UserAddressCodeRequestVO userAddressCodeRequestVO) {
        logger.info("visit ShippingAddressService deleteUserAddress");
        ShippingAddressPO shippingAddressPO = new ShippingAddressPO();
        shippingAddressPO.setId(userAddressCodeRequestVO.getAddressId());
        int deleteResult = shippingAddressDao.deleteById(shippingAddressPO);
        if (deleteResult == com.yhd.common.util.CommonConstant.ZERO){
            throw new BizException(ConstantStatusEnum.DATABASE_DELETE_EXCEPTION.getCode(), ConstantStatusEnum.DATABASE_DELETE_EXCEPTION.getDescCn());
        }
        return BusinessResponse.ok(CommonConstant.SUCCESS);
    }

    /**
     * 更新地址
     *
     * @param userAddressRequestVO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BusinessResponse<String> updateUserAddress(UserAddressRequestVO userAddressRequestVO) {
        logger.info("visit ShippingAddressService updateUserAddress");
        String contactNumber = userAddressRequestVO.getContactNumber();
        Pattern pattern = Pattern.compile(CommonConstant.MOBILE_NUMBER_REGULAR_MATCH_TEMPLATE);
        if (!pattern.matcher(contactNumber).matches()) {
            logger.error("mobile number format error :{}",contactNumber);
            throw new BizException(ConstantStatusEnum.MOBILE_NUMBER_FORMAT_ERROR.getCode(),ConstantStatusEnum.MOBILE_NUMBER_FORMAT_ERROR.getDescCn());
        }
        String id = userAddressRequestVO.getId();
        if (StringUtils.isBlank(id)){
            throw new BizException(ConstantStatusEnum.PARAM_NOT_EMPTY.getCode(), ConstantStatusEnum.PARAM_NOT_EMPTY.getDescCn());
        }
        if (StringUtils.equals(CommonConstant.TRUE,userAddressRequestVO.getDefaultStatus())){
            judgmentDefault(userAddressRequestVO.getUserCode(),userAddressRequestVO.getType());
        }
        String type = userAddressRequestVO.getType();
        if (StringUtils.isBlank(type)){
            //不传默认为收货地址
            type = CommonConstant.SHIPPING;
        }
        if (StringUtils.equals(CommonConstant.TRUE,userAddressRequestVO.getDefaultStatus())){
            judgmentDefault(userAddressRequestVO.getUserCode(),type);
        }
        ShippingAddressPO shippingAddressPO = new ShippingAddressPO();
        BeanUtils.copyProperties(userAddressRequestVO,shippingAddressPO);
        shippingAddressPO.setType(type);
        shippingAddressPO.setExceptionStatus(com.yhd.common.util.CommonConstant.FALSE);
        shippingAddressPO.setStatus(CommonConstant.ENABLED);
        shippingAddressPO.setUpdatedBy(CommonConstant.ADMIN);
        shippingAddressPO.setUpdatedDate(LocalDateTime.now());
        logger.info("shippingAddressDao updateById parameter is :{}",shippingAddressPO);
        int updateResult = shippingAddressDao.updateById(shippingAddressPO);
        logger.info("shippingAddressDao updateById result is :{}",updateResult);
        if (updateResult == com.yhd.common.util.CommonConstant.ZERO){
            throw new BizException(ConstantStatusEnum.DATABASE_DELETE_EXCEPTION.getCode(), ConstantStatusEnum.DATABASE_DELETE_EXCEPTION.getDescCn());
        }
        return BusinessResponse.ok(CommonConstant.SUCCESS);
    }

    /**
     * 查询地址集合
     *
     * @param userCodePageRequestVO
     */
    @Override
    public BusinessResponse< PageInfo<UserAddressResponseVO>> queryUserAddress(UserCodePageRequestVO userCodePageRequestVO) {
        logger.info("visit ShippingAddressService queryUserAddress");
        String type = userCodePageRequestVO.getType();
        List<UserAddressResponseVO> userAddressResponseVOS = new ArrayList<>();
        if (StringUtils.isBlank(type)){
            //不传默认查询收货地址
            type = CommonConstant.SHIPPING;
        }
        PageInfo<UserAddressResponseVO> userAddressResponseVOPageInfo = new PageInfo<>();
        LambdaQueryWrapper<ShippingAddressPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShippingAddressPO::getUserCode,userCodePageRequestVO.getUserCode())
                .eq(ShippingAddressPO::getStatus,CommonConstant.ENABLED).eq(ShippingAddressPO::getType,type)
                .orderByAsc(ShippingAddressPO::getDefaultStatus).orderByDesc(ShippingAddressPO::getUpdatedDate);
        PageMethod.startPage(userCodePageRequestVO.getPageNum(), userCodePageRequestVO.getPageSize());
        List<ShippingAddressPO> shippingAddressPOList = shippingAddressDao.selectList(queryWrapper);
        logger.info("shippingAddressDao  selectList result size is :{}",shippingAddressPOList.size());
        if (shippingAddressPOList.isEmpty()){
            userAddressResponseVOPageInfo.setList(userAddressResponseVOS);
            return BusinessResponse.ok(userAddressResponseVOPageInfo);
        }
        userAddressResponseVOS = BeanUtil.copyToList(shippingAddressPOList, UserAddressResponseVO.class);
        userAddressResponseVOPageInfo = PageUtil.of(shippingAddressPOList, userAddressResponseVOS);
        userAddressResponseVOPageInfo.setList(userAddressResponseVOS);
        return BusinessResponse.ok(userAddressResponseVOPageInfo);
    }

    /**
     * 查询剩余多少新增收货地址的数量
     *
     * @param addressTypeRequestVO
     */
    @Override
    public BusinessResponse<Integer> queryUserAddressNum(AddressTypeRequestVO addressTypeRequestVO) {
        logger.info("visit ShippingAddressService queryUserAddressNum");
        String type = addressTypeRequestVO.getType();
        //默认查询收货地址
      if (StringUtils.isBlank(type)){
         type = CommonConstant.SHIPPING;
      }
        int count = queryCreateAddressNum(addressTypeRequestVO.getUserCode(), type);

      if (StringUtils.equals(CommonConstant.SHIPPING,type)){
          return BusinessResponse.ok(CommonConstant.MAX_SHIPPING_ADDRESS_NUMBER - count);
      }else {
          return BusinessResponse.ok(com.yhd.common.util.CommonConstant.TWO - count);
      }
    }

    /**
     * 设置默认地址
     *
     * @param userAddressCodeRequestVO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BusinessResponse<String> setDefaultAddress(UserAddressCodeRequestVO userAddressCodeRequestVO) {
        logger.info("visit ShippingAddressService setDefaultAddress");
        String type = userAddressCodeRequestVO.getType();
        //默认设置收货地址
        if (StringUtils.isBlank(type)){
            type = CommonConstant.SHIPPING;
        }
        judgmentDefault(userAddressCodeRequestVO.getUserCode(),type);
        LambdaUpdateWrapper<ShippingAddressPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ShippingAddressPO::getId,userAddressCodeRequestVO.getAddressId())
                .eq(ShippingAddressPO::getUserCode,userAddressCodeRequestVO.getUserCode())
                .eq(ShippingAddressPO::getType,type)
                .set(ShippingAddressPO::getUpdatedBy,CommonConstant.ADMIN)
                .set(ShippingAddressPO::getUpdatedDate,LocalDateTime.now())
                .set(ShippingAddressPO::getDefaultStatus,CommonConstant.TRUE);
        int updateResult = shippingAddressDao.update(null, updateWrapper);
        if (updateResult == com.yhd.common.util.CommonConstant.ZERO){
            throw new BizException(ConstantStatusEnum.DATABASE_UPDATE_EXCEPTION.getCode(), ConstantStatusEnum.DATABASE_UPDATE_EXCEPTION.getDesc());
        }
        return BusinessResponse.ok(CommonConstant.SUCCESS);
    }

    /**
     * 获取地址详情
     *
     * @param userAddressCodeRequestVO
     */
    @Override
    public BusinessResponse<UserAddressResponseVO> getUserAddressDerails(UserAddressCodeRequestVO userAddressCodeRequestVO) {
        logger.info("visit ShippingAddressService getUserAddressDerails");
        LambdaQueryWrapper<ShippingAddressPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShippingAddressPO::getUserCode,userAddressCodeRequestVO.getUserCode())
                .eq(ShippingAddressPO::getId,userAddressCodeRequestVO.getAddressId());
        ShippingAddressPO shippingAddressPO = shippingAddressDao.selectOne(queryWrapper);
        if (ObjectUtils.isEmpty(shippingAddressPO)){
            throw new BizException(ConstantStatusEnum.DATA_NOT_EXIST.getCode(), ConstantStatusEnum.DATA_NOT_EXIST.getDesc());
        }
        logger.info("shippingAddressDao selectOne is :{}",shippingAddressPO);
        UserAddressResponseVO userAddressResponseVO = new UserAddressResponseVO();
        BeanUtils.copyProperties(shippingAddressPO,userAddressResponseVO);
        return BusinessResponse.ok(userAddressResponseVO);
    }

    /**
     * 管理员收货地址集合
     *
     * @param companyCode 企业编码
     */
    @Override
    public BusinessResponse<List<UserAddressResponseVO>> getCompanyAdminAddressList(String companyCode) {
        logger.info("CompanyService getCompanyAdminAddressList parameter companyCode :{}",companyCode);
        if (StringUtils.isBlank(companyCode)){
            throw new BizException(ConstantStatusEnum.PARAM_NOT_EMPTY.getCode(), ConstantStatusEnum.PARAM_NOT_EMPTY.getDescCn());
        }
        List<UserAddressResponseVO> userAddressResponseVOList = new ArrayList<>();

        //根据企业查询管理员集合
        LambdaQueryWrapper<CompanyAdministratorPO> companyAdministratorPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        companyAdministratorPOLambdaQueryWrapper.eq(CompanyAdministratorPO::getCompanyCode,companyCode);
        List<CompanyAdministratorPO> companyAdministratorPOList = companyAdministratorDao.selectList(companyAdministratorPOLambdaQueryWrapper);
        logger.info("companyAdministratorDao selectList is :{}",companyAdministratorPOList);
        if (companyAdministratorPOList.isEmpty()){
            return BusinessResponse.ok(userAddressResponseVOList);
        }
        List<String> adminUserCodeList = companyAdministratorPOList.stream().map(CompanyAdministratorPO::getUserCode).collect(Collectors.toList());

        //根据管理员用户编码获取收货地址集合
        LambdaQueryWrapper<ShippingAddressPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShippingAddressPO::getType,CommonConstant.SHIPPING).eq(ShippingAddressPO::getStatus,CommonConstant.ENABLED)
                .eq(ShippingAddressPO::getExceptionStatus,CommonConstant.FALSE).in(ShippingAddressPO::getUserCode,adminUserCodeList);
        List<ShippingAddressPO> shippingAddressPOList = shippingAddressDao.selectList(queryWrapper);
        logger.info("shippingAddressDao selectList is :{}",shippingAddressPOList);
        if (shippingAddressPOList.isEmpty()){
            return BusinessResponse.ok(userAddressResponseVOList);
        }
        userAddressResponseVOList = BeanUtil.copyToList(shippingAddressPOList, UserAddressResponseVO.class);
        return BusinessResponse.ok(userAddressResponseVOList);
    }


    /**
     * 查询剩余可以创建地址数量
     * @param userCode 用户编码
     * */
    private  int queryCreateAddressNum(String userCode,String type){
        logger.info("queryCreateAddressNum parameter is :{} type is :{}",userCode,type);
        LambdaQueryWrapper<ShippingAddressPO> shippingAddressPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        shippingAddressPOLambdaQueryWrapper.eq(ShippingAddressPO::getUserCode,userCode)
                .eq(ShippingAddressPO::getStatus, CommonConstant.ENABLED).eq(ShippingAddressPO::getType,type);
        Long count = shippingAddressDao.selectCount(shippingAddressPOLambdaQueryWrapper);
        logger.info("shippingAddressDao selectCount result is :{}",count);
        return count.intValue();
    }

    /**
     * 判断默认
     * @param userCode 用户编码
     * @param type 地址类型
     *
     * */
    private void  judgmentDefault(String userCode,String type){
        logger.info("judgmentDefault parameter userCode  is typeis ：{}  :{}",userCode,type);
        LambdaQueryWrapper<ShippingAddressPO> shippingAddressPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        //查询该用户是否有默认地址
        shippingAddressPOLambdaQueryWrapper.eq(ShippingAddressPO::getUserCode,userCode).eq(ShippingAddressPO::getStatus, CommonConstant.ENABLED)
                .eq(ShippingAddressPO::getDefaultStatus,CommonConstant.TRUE).eq(ShippingAddressPO::getType,type);
        ShippingAddressPO shippingAddressPO = shippingAddressDao.selectOne(shippingAddressPOLambdaQueryWrapper);
        logger.info("shippingAddressDao selectOne result is :{}",shippingAddressPO);
        if (!ObjectUtils.isEmpty(shippingAddressPO)){
            //如果有，把默认改成false
            shippingAddressPO.setDefaultStatus(CommonConstant.FALSE);
            int update = shippingAddressDao.updateById(shippingAddressPO);
            logger.info("shippingAddressDao updateById result is :{}",update);
            if (update == com.yhd.common.util.CommonConstant.ZERO){
                throw new BizException(ConstantStatusEnum.DATABASE_UPDATE_EXCEPTION.getCode(), ConstantStatusEnum.DATABASE_UPDATE_EXCEPTION.getDesc());
            }
        }


    }
}
