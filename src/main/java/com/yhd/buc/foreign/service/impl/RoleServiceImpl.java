package com.yhd.buc.foreign.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yhd.buc.foreign.constant.RedisConstant;
import com.yhd.buc.foreign.dao.CompanyAdministratorDao;
import com.yhd.buc.foreign.dao.RoleDAO;
import com.yhd.buc.foreign.dao.RoleRelationCompanyDAO;
import com.yhd.buc.foreign.dao.RoleRelationUserDAO;
import com.yhd.buc.foreign.enums.ConstantStatusEnum;
import com.yhd.buc.foreign.pojo.po.*;
import com.yhd.buc.foreign.pojo.vo.request.AuthRequestVO;
import com.yhd.buc.foreign.pojo.vo.response.RoleResponseVO;
import com.yhd.buc.foreign.service.api.*;
import com.yhd.common.exception.BizException;
import com.yhd.common.json.JSONUtil;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.redis.service.RedisService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id:RoleServiceImpl.java, v 0.12023/3/20 15:33 kangrong Exp $
 */
@Service
public class RoleServiceImpl implements RoleService {
    /**
     * 日志参数
     */
    private static final Logger logger = LogUtils.getLogger();

    @Resource
    private RedisService redisService;

    @Resource
    private RoleRelationUserDAO roleRelationUserDAO;

    @Resource
    private RoleDAO roleDAO;

    @Resource
    private CompanyAdministratorDao companyAdministratorDao;

    @Resource
    private RoleRelationCompanyDAO roleRelationCompanyDAO;

    @Resource
    private CompanyService companyService;





    @Resource
    private PlatformService platformService;


    /**
     * 设置角色企业信息
     * @param userCode 用户编码
     * @param platformCode 企业编码
     * @param relationRole 关联的企业角色
     * */
    private void  setCompanyRoleInfo(String userCode,String platformCode,String relationRole, RoleResponseVO roleResponseVO){
        CompanyPO companyPO = companyService.queryCompanyByUserCode(userCode);
        //查询是否关联企业
        if(ObjectUtils.isNotEmpty(companyPO)){
            //是否企业认证
            LambdaQueryWrapper<RoleRelationCompanyPO> roleRelationCompanyPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            roleRelationCompanyPOLambdaQueryWrapper.eq(RoleRelationCompanyPO::getCompanyCode,companyPO.getCompanyCode())
                    .eq(RoleRelationCompanyPO::getPlatformCode,platformCode);
            RoleRelationCompanyPO roleRelationCompanyPO = roleRelationCompanyDAO.selectOne(roleRelationCompanyPOLambdaQueryWrapper);
            logger.info("roleRelationCompanyDAO selectOne roleRelationCompanyPO result is :{}",roleRelationCompanyPO);
            if (ObjectUtils.isEmpty(roleRelationCompanyPO)){
                return;
            }
            String authenticationStatus =  StringUtils.equals(roleRelationCompanyPO.getRoleCode(),relationRole) ? CommonConstant.TRUE  : CommonConstant.FALSE;


            //设置企业角色
            LambdaQueryWrapper<RolePO> companyRolePOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            companyRolePOLambdaQueryWrapper.eq(RolePO::getRoleType, com.yhd.buc.foreign.constant.CommonConstant.COMPANY)
                    .eq(RolePO::getRoleCode,roleRelationCompanyPO.getRoleCode());
            RolePO companyRole = roleDAO.selectOne(companyRolePOLambdaQueryWrapper);
            logger.info("roleDAO selectOne companyRole RolePO result is :{}",companyRole);
            if (ObjectUtils.isEmpty(companyRole)){
                return;
            }
            roleResponseVO.setCompanyRole(companyRole.getRoleCode());
            roleResponseVO.setCompanyRoleName(companyRole.getRoleName());
            roleResponseVO.setAuthenticationStatus(authenticationStatus);
        }else {
            //没有企业这一个字段默认为false
            roleResponseVO.setAuthenticationStatus(CommonConstant.FALSE);
        }

    }

    /**
     * 获取用户角色
     *
     * @param requestGetAuthVO
     */
    @Override
    public BusinessResponse<RoleResponseVO> getRoleInfo(AuthRequestVO requestGetAuthVO) {
        logger.info("RoleService getAuthorityInfo parameter is :{} ",requestGetAuthVO);
        String userCode = requestGetAuthVO.getUserCode();
        String platformCode = requestGetAuthVO.getPlatformCode();

        //判断平台是否有误
        List<Object> list = platformService.queryPlatformCodeList();
        if (!list.contains(platformCode)){
            throw new BizException(ConstantStatusEnum.PLATFORM_HAS_BEEN_DISABLED.getCode(), ConstantStatusEnum.PLATFORM_HAS_BEEN_DISABLED.getDescCn());
        }

        String redisTable = RedisConstant.YHD_SERVICE_BUC_USER_ROLE_INFO;
        RoleResponseVO roleResponseVO = new RoleResponseVO();
        roleResponseVO.setUserCode(userCode);
        //同一用户在不同平台下呈现的角色不一样
        String redisKey =  platformCode + CommonConstant.DASH  + userCode;
        if (!redisService.hHasKey(redisTable,redisKey)){
            //查询用户角色关联
            LambdaQueryWrapper<RoleRelationUserPO> roleRelationUserPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            roleRelationUserPOLambdaQueryWrapper.eq(RoleRelationUserPO::getUserCode,userCode).eq(RoleRelationUserPO::getPlatformCode,platformCode);
            RoleRelationUserPO roleRelationUserPO = roleRelationUserDAO.selectOne(roleRelationUserPOLambdaQueryWrapper);
            logger.info("getAuthorityInfo roleRelationUserDAO selectOne result is :{}",roleRelationUserPO);
            if (ObjectUtils.isEmpty(roleRelationUserPO)){
                //没有用户角色,直接返回
                String authorityResponseVOJson = JSONUtil.toJson(roleResponseVO);
                logger.info("redisService set  key is :{} value :{}" ,redisKey,authorityResponseVOJson);
                Boolean setRedisResult = redisService.hset(redisTable, redisKey,authorityResponseVOJson);
                logger.info("redisService set result is :{}",setRedisResult);
                return BusinessResponse.ok(null);
            }

            //查询用户角色信息
            LambdaQueryWrapper<RolePO> rolePOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            rolePOLambdaQueryWrapper.eq(RolePO::getRoleType, com.yhd.buc.foreign.constant.CommonConstant.USER)
                    .eq(RolePO::getRoleCode,roleRelationUserPO.getRoleCode());
            RolePO rolePO = roleDAO.selectOne(rolePOLambdaQueryWrapper);
            logger.info("getAuthorityInfo roleDAO selectOne user role result is :{}",rolePO);
            if (ObjectUtils.isEmpty(rolePO)){
                return BusinessResponse.ok(null);
            }
            roleResponseVO.setUserRole(rolePO.getRoleCode());
            roleResponseVO.setRoleName(rolePO.getRoleName());

            //设置角色企业信息
            setCompanyRoleInfo(userCode,platformCode,rolePO.getRelationRole(),roleResponseVO);

            roleResponseVO.setPlatformCode(platformCode);
            String authorityResponseVOJson = JSONUtil.toJson(roleResponseVO);
            logger.info("redisService set  key is :{} value :{}" ,redisKey,authorityResponseVOJson);
            Boolean setRedisResult = redisService.hset(redisTable, redisKey,authorityResponseVOJson);
            logger.info("redisService set result is :{}",setRedisResult);
        }else {
            roleResponseVO  =  JSONUtil.fromObject(redisService.hget(redisTable, redisKey).toString(),RoleResponseVO.class);
        }
        return BusinessResponse.ok(roleResponseVO);
    }
}
