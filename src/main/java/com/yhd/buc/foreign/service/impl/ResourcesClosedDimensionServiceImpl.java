package com.yhd.buc.foreign.service.impl;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yhd.buc.foreign.constant.RedisConstant;
import com.yhd.buc.foreign.dao.ResourcesClosedDimensionDao;
import com.yhd.buc.foreign.pojo.po.ResourcesClosedDimensionPO;
import com.yhd.buc.foreign.service.api.ResourcesClosedDimensionService;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.redis.service.RedisService;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id:PlatformService.java, v 0.12023/3/21 15:23 kangrong Exp $
 */
@Service
public class ResourcesClosedDimensionServiceImpl implements ResourcesClosedDimensionService {

    private static final Logger logger = LogUtils.getLogger();

    @Resource
    private RedisService redisService;

    @Resource
    private ResourcesClosedDimensionDao resourcesClosedDimensionDao;


    /**
     * 根据编码查询各个维度的禁用权限
     *
     * @param platformCode 平台编码
     * @param codeMap     编码Map key：类型 value：编码
     */
    @Override
    public List<String> queryClosedDimensionListByPlatformAndCode(String platformCode, Map<String, String> codeMap) {
        logger.info("visit  queryClosedDimensionListByPlatformAndCode platformCode is :{}",platformCode);
        List<String> closedDimensionList = new ArrayList<>();
            Iterator<Map.Entry<String, String>> iterator = codeMap.entrySet().iterator();
            //取出类型和编码查询维度禁用权限
            while (iterator.hasNext()){
                Map.Entry<String, String> next = iterator.next();
                String key = next.getKey();
                String value = next.getValue();
                List<String> closedDimensionRedis = getClosedDimensionRedis(platformCode, key,value);
                if (CollectionUtils.isNotEmpty(closedDimensionRedis)){
                    closedDimensionList.addAll(closedDimensionRedis);
                }
            }
        return closedDimensionList;
    }


    /**
     * 设置redis取出
     * @param platformCode 平台编码
     * @param type 类型
     * @param code 类型加编码（用户/企业/角色/平台）
     *
     * */
    private List<String> getClosedDimensionRedis(String platformCode,String type,String code){
        logger.info("redisService set YHD_SERVICE_BUC_RESOURCES_CONTROL platformCode :{} type :{} code :{}",platformCode,type,code);
        String key = type + CommonConstant.DASH + code;
        List<String> closedDimensionList = new ArrayList<>();
        if (redisService.hHasKey(RedisConstant.YHD_SERVICE_BUC_RESOURCES_CONTROL + platformCode, key)){
            closedDimensionList = redisService.hget(RedisConstant.YHD_SERVICE_BUC_RESOURCES_CONTROL + platformCode, key);
            logger.info("redisService get YHD_SERVICE_BUC_RESOURCES_CONTROL key :{} result :{}",key,closedDimensionList);
        }else {
            LambdaQueryWrapper<ResourcesClosedDimensionPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ResourcesClosedDimensionPO::getPlatformCode,platformCode)
                    .eq(ResourcesClosedDimensionPO::getType,type).eq(ResourcesClosedDimensionPO::getDimensionCode,code);
            List<ResourcesClosedDimensionPO> resourcesClosedDimensionPOList = resourcesClosedDimensionDao.selectList(wrapper);
            if (!resourcesClosedDimensionPOList.isEmpty()){
                closedDimensionList = resourcesClosedDimensionPOList.stream().map(ResourcesClosedDimensionPO::getResourcesCode).collect(Collectors.toList());
            }
            //如果没有禁用权限就放一个空数组作为缓存
            redisService.hset(RedisConstant.YHD_SERVICE_BUC_RESOURCES_CONTROL + platformCode,key,closedDimensionList);
        }
        return closedDimensionList;
    }

}
