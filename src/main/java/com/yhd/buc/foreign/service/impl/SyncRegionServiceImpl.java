package com.yhd.buc.foreign.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yhd.buc.foreign.constant.CommonConstant;
import com.yhd.buc.foreign.constant.RedisConstant;
import com.yhd.buc.foreign.dao.ComHomeDao;
import com.yhd.buc.foreign.dao.SyncRegionDAO;
import com.yhd.buc.foreign.enums.ConstantStatusEnum;
import com.yhd.buc.foreign.pojo.po.ComHomePO;
import com.yhd.buc.foreign.pojo.po.SyncRegionPO;
import com.yhd.buc.foreign.pojo.vo.request.LinkRequestVO;
import com.yhd.buc.foreign.pojo.vo.request.PlatformCodeRequest;
import com.yhd.buc.foreign.pojo.vo.request.RegionRequestVO;
import com.yhd.buc.foreign.pojo.vo.response.ComHomeDetailsResponseVO;
import com.yhd.buc.foreign.pojo.vo.response.SyncRegionResponseVO;
import com.yhd.buc.foreign.pojo.vo.response.UserBaseInfoResponseVO;
import com.yhd.buc.foreign.service.api.ComHomeService;
import com.yhd.buc.foreign.service.api.SyncRegionService;
import com.yhd.common.exception.BizException;
import com.yhd.common.json.JSONUtil;
import com.yhd.common.pojo.vo.BusinessResponse;
import com.yhd.common.util.LogUtils;
import com.yhd.redis.service.RedisService;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id:ShippingAddressServiceImpl.java, v 0.12023/3/9 15:22 kangrong Exp $
 */
@Service
public class SyncRegionServiceImpl implements SyncRegionService {

    private static final Logger logger = LogUtils.getLogger();

    @Resource
    private SyncRegionDAO syncRegionDAO;

    /**
     * redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 获取地区集合
     *
     * @param regionRequestVO
     */
    @Override
    public BusinessResponse<List<SyncRegionResponseVO>> getRegionList(RegionRequestVO regionRequestVO) {
        logger.info("start getRegionList parameter is :{}", regionRequestVO);
        List<SyncRegionResponseVO> syncRegionResponseVOList = new ArrayList<>();
        //判断key是否存在
        if (!redisService.hasKey(RedisConstant.REGION_REDIS_LIST_VALUE)){
            logger.info("redisService has no key");
            //查询所有
            List<SyncRegionPO> syncRegionPOList = syncRegionDAO.selectList(new LambdaQueryWrapper<>());
            if (CollUtil.isNotEmpty(syncRegionPOList)){
                List<SyncRegionResponseVO>  allSyncRegionResponseVOS = BeanUtil.copyToList(syncRegionPOList, SyncRegionResponseVO.class);
                Map<String, List<SyncRegionResponseVO>> listMap = allSyncRegionResponseVOS.stream().collect(Collectors.groupingBy(SyncRegionResponseVO::getParentId));

                //将map的值转JSON
                Map<String, String> redisRegionMap = new HashMap<>();
                for (Map.Entry<String, List<SyncRegionResponseVO>> regionHashMapEntry : listMap.entrySet()) {
                    redisRegionMap.put(regionHashMapEntry.getKey(), JSONUtil.toJson(regionHashMapEntry.getValue()));
                }

                //存入redis
                redisService.batchHmset(RedisConstant.REGION_REDIS_LIST_VALUE,redisRegionMap);
                redisService.expire(RedisConstant.REGION_REDIS_LIST_VALUE, RedisConstant.FIVE_DAY);
                syncRegionResponseVOList = listMap.get(regionRequestVO.getRegionId());
            }
        }else {
            //从缓存拿数据
            Object regionObject = redisService.hget(RedisConstant.REGION_REDIS_LIST_VALUE, regionRequestVO.getRegionId());
            if (ObjectUtils.isNotEmpty(regionObject)){
                syncRegionResponseVOList =  cn.hutool.json.JSONUtil.toList(regionObject.toString(),SyncRegionResponseVO.class);
            }

        }

        return BusinessResponse.ok(syncRegionResponseVOList);
    }
}
