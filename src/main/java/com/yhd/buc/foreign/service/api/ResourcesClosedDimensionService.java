package com.yhd.buc.foreign.service.api;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version Id:ResourcesClosedDimensionService.java, v 0.12023/3/22 14:31 kangrong Exp $
 */
public interface ResourcesClosedDimensionService {


    /**
     * 根据编码查询各个维度的禁用权限
     * @param codeMap     编码Map key：类型 value：编码
     * @param platformCode 平台编码
     * */
    List<String> queryClosedDimensionListByPlatformAndCode(String platformCode, Map<String, String> codeMap);

}
