package com.yhd.buc.foreign.common;

import com.yhd.common.pojo.vo.BusinessResponse;

/**
 * <AUTHOR>
 * @version Id:BusinessResponseCommon.java, v 0.12022/5/25 11:30 kangrong Exp $
 */
public class BusinessResponseCommon {
    private BusinessResponseCommon() {
    }

    private static final long serialVersionUID = -3865115480476574285L;

    /**
     * 成功编码
     */
    private static final int RESPONSE_OK = 0;

    /**
     * 成功语句
     */
    private static final String RESPONSE_OK_MSG = "success";

    /**
     * 失败编码
     */
    private static final int RESPONSE_ERROR = 500000;

    /**
     * 失败语句
     */
    public static final String RESPONSE_ERROR_MSG = "服务异常";

    /**
     * 成功
     *
     * @param data 返回的数据
     * @param <T>
     * @return
     */
    public static <T> BusinessResponse<T> ok(T data) {
        BusinessResponse<T> businessResponse = new BusinessResponse<>();
        businessResponse.setRt_code(RESPONSE_OK);
        businessResponse.setRt_msg(RESPONSE_OK_MSG);
        businessResponse.setData(data);

        return businessResponse;
    }

    /**
     * 错误失败的返回
     *
     * @param <T>
     * @return
     */
    public static <T> BusinessResponse<T> fail() {
        return fail(RESPONSE_ERROR_MSG);
    }

    /**
     * 错误失败的返回
     *
     * @param msg 错误提示语
     * @param <T>
     * @return
     */
    public static <T> BusinessResponse<T> fail(String msg) {
        return fail(RESPONSE_ERROR, msg);
    }

    /**
     * 错误失败的返回
     *
     * @param code 错误失败的编码
     * @param msg  错误失败的提示语
     * @param <T>
     * @return
     */
    public static <T> BusinessResponse<T> fail(int code, String msg) {
        BusinessResponse<T> businessResponse = new BusinessResponse<>();
        businessResponse.setRt_code(code);
        businessResponse.setRt_msg(msg);
        return businessResponse;
    }

}
