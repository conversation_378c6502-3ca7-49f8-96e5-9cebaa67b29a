package com.yhd.buc.foreign.common;

import com.yhd.buc.foreign.constant.CommonConstant;
import com.yhd.buc.foreign.constant.RedisConstant;
import com.yhd.buc.foreign.enums.ConstantStatusEnum;
import com.yhd.common.exception.BizException;
import com.yhd.common.util.LogUtils;
import com.yhd.redis.service.RedisService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version Id:VerifyCommon.java, v 0.12022/6/15 14:36 kangrong Exp $
 * 认证公用方法
 */
public class VerifyCommon {
    private VerifyCommon() {
    }

    /**
     * 日志参数
     */
    private static final Logger logger = LogUtils.getLogger();



    /**
     * 验证邮箱
     * @param email 邮箱
     * */
    public static void validationEmail(String email){
        if (StringUtils.isBlank(email)){
            logger.error("registerAccount email is null ");
            throw new BizException(ConstantStatusEnum.PARAM_NOT_EMPTY.getCode(),ConstantStatusEnum.PARAM_NOT_EMPTY.getDesc());
        }
        //验证邮箱格式
        Pattern pattern = Pattern.compile(CommonConstant.EMAIL_REGULAR_MATCH_TEMPLATE);

        if (!pattern.matcher(email).matches()) {
            logger.error("Email format error :{}",email);
            throw new BizException(ConstantStatusEnum.EMAIL_FORMAT_ERROR.getCode(),ConstantStatusEnum.EMAIL_FORMAT_ERROR.getDesc());
        }
    }

    /**
     * 验证手机
     * @param mobileNumber 手机
     * */
    public static void validationMobile(String mobileNumber){
        if (StringUtils.isBlank(mobileNumber)){
            logger.error("registerAccount mobileNumber is null ");
            throw new BizException(ConstantStatusEnum.PARAM_NOT_EMPTY.getCode(),ConstantStatusEnum.PARAM_NOT_EMPTY.getDesc());
        }
        //验证手机格式
        Pattern pattern = Pattern.compile(CommonConstant.MOBILE_NUMBER_REGULAR_MATCH_TEMPLATE);
        if (!pattern.matcher(mobileNumber).matches()) {
            logger.error("mobile number format error :{}",mobileNumber);
            throw new BizException(ConstantStatusEnum.MOBILE_NUMBER_FORMAT_ERROR.getCode(),ConstantStatusEnum.MOBILE_NUMBER_FORMAT_ERROR.getDescCn());
        }
    }



    /**
     * 判断验证码
     * @param account 账号
     * @param functionType  功能类型
     * */
    public static void  decisionVerificationCode(RedisService redisClientService,String account, String functionType, String verificationCode){
        String key =  RedisConstant.YHD_BUC_FOREIGN + RedisConstant.REGISTRATION_VERIFICATION_CODE+ functionType + account;
        String value = (String) redisClientService.get(key);
        redisClientService.del(key);
        logger.info("decisionVerificationCode account :{} decisionVerificationCode is key:{} ",account,value);
        if (StringUtils.isBlank(value)){
            logger.error("verification code not exists");
            throw new BizException(ConstantStatusEnum.VERIFICATION_CODE_ERROR.getCode(),ConstantStatusEnum.VERIFICATION_CODE_ERROR.getDescCn());
        }
        if (!StringUtils.equals(value,verificationCode)){
            logger.error("verification code error");
            throw new BizException(ConstantStatusEnum.VERIFICATION_CODE_ERROR.getCode(),ConstantStatusEnum.VERIFICATION_CODE_ERROR.getDescCn());
        }
    }


}
