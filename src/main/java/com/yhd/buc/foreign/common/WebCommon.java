package com.yhd.buc.foreign.common;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;

/**
 * <AUTHOR>
 * @version Id:WebCommon.java, v 0.12022/5/27 9:26 kangrong Exp $
 */
public class WebCommon {

    private WebCommon() {
    }


    /**
     * 收集错误信息
     * */
    public static String collectingErrorMessage(BindingResult result){
        StringBuilder errorMessageBuilder = new StringBuilder();
        for (ObjectError error :result.getAllErrors()) {
            errorMessageBuilder.append(error.getDefaultMessage()).append(";");
        }
        return errorMessageBuilder.toString();
    }
}
