package com.yhd.buc.foreign.constant;

/**
 * <AUTHOR>
 * @version Id:CommonConstant.java, v 0.12022/5/25 15:23 kangrong Exp $
 */
public class CommonConstant {
    private CommonConstant() {
    }

    /**
     * 成功编码
     */
    public static final int RESPONSE_OK = 0;

    public static final int    CALL_TYPE_INNER                  = 1;
    public static final int    CALL_TYPE_OUTER                  = 2;

    public static final String  SUCCESS_STATUS_CODE           = "200";

    //收货地址地址最大量
    public static final int    MAX_SHIPPING_ADDRESS_NUMBER                   = 30;

    public static final int    MAX_INVOICE_ADDRESS_NUMBER                   = 20;


    public static final String UTF8                                 = "utf-8";

    public static final String SYMBOL_QUOTE                             = "'";

    public static final String SYMBOL_COMMA                             = ",";

    //创建人默认ADMIN
    public static final String ADMIN                             = "admin";

    //邮件模块
    public static final String EMAIL = "email";

    //短信模块
    public static final String MOBILE = "mobile";

    //注册
    public static final String REGISTER = "register";

    //忘记密码
    public static final String FORGET_PASSWORD = "forgetPassword";

    //绑定用户
    public static final String BIND_USER = "bindUser";


    // 启用
    public static  final String ENABLED = "enabled";

    // 禁用
    public static  final String DISABLED = "disabled";

    // 成功
    public static  final String SUCCESS = "success";

    // QQ
    public static  final String QQ = "qq";

    // 微信
    public static  final String WECHAT = "wechat";

    public static  final String UPGRADE = "upgrade";                                                                    //升级

    public static  final String DEMOTION = "demotion";                                                                  //降级

    public static  final String REJECTED = "rejected";                                                                  //驳回




    //拉黑状态
    public static final String BLACK = "black";

    public static  final String FALSE = "false";                                                                         // 否

    public static  final String TRUE = "true";                                                                           // 否

    // 微信
    public static  final String WAIT_STATUS = "wait";

    public static  final String FINISH = "finish";                                                                       // 完成

    public static  final String USER_CODE = "userCode";                                                                  // 用户编码

    public static  final String NORMAL = "normal";                                                                      // 正常




    public static  final String PASS = "pass";                                                                           // 审核通过

    public static  final String REFUSE = "refuse";                                                                       // 审核不通过

    public static  final String INSERT = "insert";                                                                       // 增加

    public static  final String UPDATE = "update";                                                                       // 更新

    public static  final String DELETE = "delete";                                                                       // 删除



    /**
     * 注册常量
     * */
    public static final String EMAIL_REGULAR_MATCH_TEMPLATE = "^\\w[-\\w.+]*@([A-Za-z0-9][-A-Za-z0-9]+\\.)+[A-Za-z]{2,14}$";  //电子邮件正则表达式匹配模板
    public static final String MOBILE_NUMBER_REGULAR_MATCH_TEMPLATE = "^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$";  //手机号码的正则表达式



    public static final String PC_SOURCE = "PC";                                                                         //注册来源PC端






    /**
     *
     * 钉钉常量
     * */

    /**
     * 数据库常量
     * */
    //用户表名
    public static final String USER_TABLE_NAME = "yhdbuc_us_user_info";

    //企业表名
    public static final String COMPANY_TABLE_NAME = "yhdbuc_cp_company_info";

    //角色表名
    public static final String AUTH_TABLE_NAME = "yhdbuc_auth_role_info";

    /**
     * 发送验证码验证类型
     * */

    //注册用户
    public static final String REGISTERUSER = "registerUser";



    /**
     * 第三方使用常量
     * */
    //解绑QQ
    public static final String UNLINK_QQ = "unlinkQQ";

    //解绑微信
    public static final String UNLINK_WECHAT = "unlinkWechat";

    //企查查模糊搜索
    public static final String QCC_FUZZY_QUERY = "fuzzyQuery";

    //企查查精确搜索
    public static final String QCC_PRECISE_QUERY = "preciseQuery";



    /**
     * 收货地址常量
     * */
    //发票地址
    public static final String INVOICE = "invoice";

    //收货地址
    public static final String SHIPPING = "shipping";


    /**
     * 企业模块
     * */
    //普通企业申请
    public static final String ORDINARY = "ordinary";

    //认证企业申请
    public static final String AUTHENTICATION = "authentication";


    //未审核
    public static final String UNREVIEWED = "unreviewed";


    /**
     * 企业用户组类型
     * */
    //企业
    public static final String COMPANY = "company";

    //用户
    public static final String USER = "user";

    //用户角色
    public static final String USER_ROLE = "user_role";

    //企业角色
    public static final String COMPANY_ROLE = "company_role";

    //平台
    public static final String PLATFORM = "platform";

    public static final String NO_ENTERPRISE = "暂无企业";
}


