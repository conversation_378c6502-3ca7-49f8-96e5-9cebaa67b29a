package com.yhd.buc.foreign.constant;

/**
 * <AUTHOR>
 * @version Id:RouteConstant.java, v 0.12022/5/25 11:21 kangrong Exp $
 */
public class RouteConstant {
    private RouteConstant() {
    }

    /***** 公共 *****/

    public static final  String PREFIX = "/buc/foreign";                                                             // 路由前缀

    public static final  String VERSION = "/v1/0";                                                                       // 版本号

    public static final  String INITIALIZATION = "/initialization";                                                      // 初始化

    public static final  String GET = "/get";                                                                            // 获取

    public static final  String GET_LIST = "/getList";                                                                   // 获取列表

    public static final  String INFO = "/info";                                                                          // 信息

    public static final  String UPDATE = "/update";                                                                      // 更新

    public static final  String DELETE = "/delete";                                                                      // 删除

    public static final  String ADD = "/add";                                                                            // 添加

    public static final  String DERAILS = "/details";                                                                    //详情

    public static final  String TEST = "/test";                                                                          // 测试

    public static final  String CHECK = "/check";                                                                        // 检查

    public static final  String COUNT = "/count";                                                                        // 数量

    public static final  String SET_DEFAULT = "/setDefault";                                                            //设置默认

    public static final  String SURPLUS_NUMBER = "/surplus/number";                                                      //剩余数量



    public static final  String AUTH = "/auth";                                                                         //权限模块

    public static final  String ADDRESS = "/address";                                                                    //地址模块


    /**
     * 主页配置
     */
    public static final String COM_HOME = "/com/home";

    public static final  String HEAD_PORTRAIT = "/headPortrait";                                                        //地址模块


    public static final String UPLOAD = "/upload";                                                                      //上传

    public static final String PICTURE = "/picture";                                                                      //图片


    public static final  String ACCOUNT = "/account";                                                                    //账户模块

    public static final  String VERIFICATION = "/verification";                                                          //验证

    public static final  String AGAIN_VERIFICATION = "/again/verification";                                               //再次验证

    public static final  String FORGETTING_PASSWORD_VERIFY = "/forgetting/password/verify";                              //忘记密码验证


    public static final  String FORGETTING_RESET_PASSWORD = "/forgetting/reset/password";                               //忘记密码（重置密码）


    public static final  String JUDGE_IS_ENABLE = "/judge/isEnable";                                                     //判断账户是否可用





    public static final  String REGISTER = "/register";                                                                  //注册

    public static final  String SEND_VERIFICATION_CODE = "/send/VerificationCode";                                       //发送验证码



    public static final  String UPDATE_PASSWORD = "/updatePassword";                                                     //更新账户密码


    public static final  String VERIFY_EMAIL = "/verify/email";                                                          //验证邮箱


    public static final  String SYNCHRONIZE = "/synchronize";                                                            //同步模块

    public static final  String SYNC_ERP_COMPANY_INFO = "/syncErpCompanyInfo";                                           //同步ERP企业数据给电商



    /***** 登录 *****/
    public static final  String LOGIN = "/login";                                                                        // 登录

    public static final  String LOGOUT = "/logout";                                                                      // 登出

    public static final  String WECHAT_REDIRECT= "/wechat/redirect";                                                     // 微信重定向

    public static final  String QQ_REDIRECT= "/qq/redirect";                                                             // QQ重定向

    public static final  String QQ_GET_USER_INFO = "/qq/getUserInfo";                                                    // 获取QQ用户信息

    public static final  String WECHAT_GET_USER_INFO = "/wechat/getUserInfo";                                             // 获取QQ用户信息






    /***** 注册 *****/
    public static final  String IS_EXIST = "/isExist";                                                                   //是否存在

    public static final  String IS_BIND = "/isBind";                                                                   //是否存在

    public static final  String BINDING_THIRD_PARTY_ACCOUNT = "/binding/thirdParty/account";                            //第三方绑定

    public static final  String BIND = "/bind";                                                                         //绑定

    public static final  String VERIFY_OLD_ACCOUNT = "/verify/oldAccount";                                              //绑定功能：验证旧手机

    public static final  String VERIFY_NEW_ACCOUNT = "/verify/newAccount";                                              //绑定功能：验证新手机





    /***** 权限 *****/



    /***** 第三方调用 *****/
    public static final  String APIS = "/apis";                                                                          // 第三方调用前缀


    public static final  String USER_INFO_BY_USER_CODE = "/userInfoByUserCode";                                              // 根据UserCode用户信息                                                                // 测试



    /***** 信息模块 *****/

    public static final  String SEND_MESSAGE = "/send/message";                                                //发送消息模块

    public static final  String DINGDING = "/dingding";                                                        //钉钉

    public static final  String MOBILE = "/mobile";                                                            //手机

    public static final  String EMAIL = "/email";                                                              //邮件



    /***** 调用第三方 *****/

    public static final  String UNLINK = "/unlink";                                                                    //解绑

    public static final  String THIRD_PARTY = "/thirdParty";                                                            //企查查

    public static final  String QUERY_ENTERPRISE = "/query/enterprise";                                                  //查询企业

    /***** 前台服务 *****/
    public static final  String FRONT = "/front";                                                                       // 前台项目调用


    /***** 企业模块 *****/
    public static final  String COMPANY = "/company";                                                                    //企业模块

    public static final  String BY_COMPANY_CODE = "/by/companyCode";                                                     //根据企业编码

    public static final  String JOIN_ENTERPRISE = "/join/enterprise";                                                    //加入企业

    public static final  String EXIT_ENTERPRISE = "/exit/enterprise";                                                    //退出企业

    public static final  String APPLICANT = "/applicant";                                                               //申请人

    public static final  String OFFLINE = "/offline";                                                                   //线下


    /***** 平台模块 *****/
    public static final  String PLATFORM = "/platform";                                                                    //企业模块

    public static final  String BY_PLATFORM_CODE = "/by/platformCode";                                                     //根据企业编码

    public static final  String REGION = "/region";


    /**
     * 账户模块
     * */
    public static final  String QQ_ACCOUNT = "/qqAccount";                                                               //QQ账户

    public static final  String WECHAT_ACCOUNT = "/wechatAccount";                                                       //微信账户

    /**
     * 用户模块
     * */
    public static final  String USER = "/user";                                                                         //用户模块


    public static final  String USER_LIST = "/userList";                                                                //用户集合
    public static final  String USER_LIST_PAGE = "/userList/page";                                                                //用户集合

    public static final  String USER_NUMBER = "/userNumber";                                                            //用户数量

    public static final  String ROLE = "/role";                                                                         //角色模块

    public static final  String BASE_INFORMATION = "/base/information";                                                  //用户基本信息

    public static final  String ALL_INFORMATION = "/all/information";                                                    //用户所有信息


    /**
     * 用户模块
     * */
    public static final  String ADMIN = "/admin";                                                                        //管理员模块


    public  static final String NOT_NULL_MESSAGE_TEMPLATE = "%s不能为空";

    public  static final String LENGTH_MESSAGE_MESSAGE_TEMPLATE = "%s长度限制";

    public  static final String NOT_CONFORM_TO_THE_REGULATION = "%s不符合规范";

}
