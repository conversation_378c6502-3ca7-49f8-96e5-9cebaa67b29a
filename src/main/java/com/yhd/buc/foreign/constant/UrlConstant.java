package com.yhd.buc.foreign.constant;

/**
 * <AUTHOR>
 * @version Id:UrlConstant.java, v 0.12022/2/8 11:12 kangrong Exp $
 */
public class UrlConstant {
    private UrlConstant() {
    }

    public static final String PARAMETER_LINK_SYMBOL = "&";
    /**
     * 微信URL
     * */

    //获取微信授权二维码的URL
    public static final String GET_WHCHAT_COED_URL = "https://open.weixin.qq.com/connect/qrconnect?response_type=code&scope=snsapi_login&appid=";

    //重定向URL
    public static final String REDIRECT_URL = "&redirect_uri=";

    //微信回调地址
    public static final String WECHAT_CALLBACK_URL = "http://test.user.center.yiheda.com/user/center/v1/0/login/wechat/getUserInfo";

    //微信获取Token地址
    public static final String WECHAT_GET_TOKEN = "https://api.weixin.qq.com/sns/oauth2/access_token?grant_type=authorization_code&code=";

    //微信获取openId地址
    public static final String WECHAT_GET_OPENID = "https://api.weixin.qq.com/sns/oauth2/refresh_token?grant_type=refresh_token&refresh_token=";

    //微信获取用户信息地址
    public static final String WECHAT_GET_USERINFO = "https://api.weixin.qq.com/sns/userinfo?access_token=";


    //应用密钥AppSecret，在微信开放平台提交应用审核通过后获得
    public static final String WECHAT_SECRET = "&secret=";

    //应用唯一标识，在微信开放平台提交应用审核通过后获得
    public static final String WECHAT_APPID = "&appid=";

    //用于保持请求和回调的状态，授权请求后原样带回给第三方。(加密字符串用来防止被攻击)
    public static final String STATE = "&state=";

    ///微信重定向尾缀（固定）
    public static final String WECHAT_REDIRECT  = "#wechat_redirect";




    /**
     * QQ的URL
     * */
    //获取QQ授权二维码的URL
    public static final String GET_QQ_CODE_URL = "https://graph.qq.com/oauth2.0/show?which=Login&display=pc&redirect_uri=http://test.user.center.yiheda.com/user/center/v1/0/login/qq/getUserInfo&scope=get_user_info&response_type=code";


    //获取QQ的OPENID的URL
    public static final String GET_QQ_OPEN_ID = "https://graph.qq.com/oauth2.0/me?fmt=json&access_token=";


    //获取QQ的Access Token的URL
    public static final String GET_QQ_ACCESS_TOKEN_URL = "https://graph.qq.com/oauth2.0/token?grant_type=authorization_code&fmt=json&redirect_uri=http://test.user.center.yiheda.com/user/center/v1/0/login/qq/getUserInfo&code=";

    //QQ客户端ID
    public static final String  QQ_CLIENT_ID ="&client_id=";

    //QQ客户端秘钥
    public static final String QQ_CLIENT_SECRET ="&client_secret=";

    //获取QQ用户信息URL
    public static final String GET_QQ_USER_INFO = "https://graph.qq.com/user/get_user_info?access_token=";

    //申请QQ登录成功后，分配给应用的appid
    public static final String OAUTH_CONSUMER_KEY ="&oauth_consumer_key=";

    //用户的ID，与QQ号码一一对应。
    public static final String OPENID = "&openid=";


    /**
     * 企查查的URL
     * */

    //企查查企业工商模糊搜索URL
    public static final String QCC_FUZZY_QUERY = "http://api.qichacha.com/FuzzySearch/GetList?key=";

    //企信宝企业工商模糊搜索URL
    public static final String QXB_FUZZY_QUERY = "http://api.qixin.com/APIService/v2/search/advSearch?appkey=";

    //企查查企业工商精确搜索URL
    public static final String QCC_PRECISE_QUERY = "http://api.qichacha.com/ECIV4/SearchWide?key=";

    //企信宝企业工商精确搜索URL
    public static final String QXB_PRECISE_QUERY = "https://api.qixin.com/APIService/enterprise/getBasicInfo?appkey=";

    //企查查模糊搜索关键字
    public static final String QCC_SEARCH_KEYWORDS =  "&searchKey=";

    //企查查精确搜索关键字
    public static final String QCC_KEYWORD =  "&keyword=";

    //企信宝秘钥常量
    public static final String QXB_SECRET_KEY =  "&secret_key=";






}
