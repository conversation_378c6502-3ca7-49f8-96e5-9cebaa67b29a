package com.yhd.buc.foreign.constant;

/**
 * <AUTHOR>
 * @version Id:RedisConstant.java, v 0.12022/5/25 9:43 kangrong Exp $
 * redis常量
 */
public class RedisConstant {
    private RedisConstant() {
    }

    //用户中心公用键
    public static final String YHD_SERVICE_BUC_API =  "YHD_SERVICE_BUC:";

    //用户中心用户个人信息key
    public static final String USER_INFO =  "_USER_INFO_";

    //用户中心用户线上企业信息key
    public static final String COMPANY_INFO =  "_COMPANY_INFO_";

    //用户中心用户线下企业信息key
    public static final String OFFLINE_COMPANY_INFO =  "_OFFLINE_COMPANY_INFO_";
    //用户中心用户信息key

    public static final String AUTH_INFO =  "_AUTH_INFO_";

    //平台信息
    public static final String PLATFORM =  "PLATFORM_";

    //全部平台
    public static final String ALL_PLATFORM_VALUE =  "ALL_PLATFORM_VALUE";



    //用于redis缓存记录IP的键
    public static final String YHD_SERVICE_USER_CENTER_RECORD_IP =  "YHD_SERVICE_USER_CENTER_RECORD_IP-";

    //用于redis缓存记录账号的键
    public static final String YHD_SERVICE_USER_CENTER_RECORD_ACCOUNT =  "YHD_SERVICE_USER_CENTER_RECORD_ACCOUNT-";




    //项目的通用键
    public static final String SEND_SUCCESS =  "SEND_SUCCESS";
    //项目的通用键
    public static final String SEND_FAIL =  "SEND_FAIL";
    //项目的通用键
    public static final String YHD_BUC_FOREIGN =  "YHD_BUC_FOREIGN:";
    //验证码
    public static final String REGISTRATION_VERIFICATION_CODE =  "REGISTRATION_VERIFICATION_CODE_";
    //发送验证码次数
    public static final String SEND_VERIFICATION_CODE_COUNT =  "SEND_VERIFICATION_CODE_COUNT_";
    //错误次数记录
    public static final String ERROR_RECORD =  "ERROR_RECORD_";
    //最大次数记录
    public static final String MAXIMUM_FREQUENCY_RECORD =  "MAXIMUM_FREQUENCY_RECORD_";





    //三十秒内
    public static final String WITHIN_THIRTY_SECONDS =  "WITHIN_THIRTY_SECONDS-";

    //十分钟内
    public static final String WITHIN_TEN_MINUTES =  "WITHIN_TEN_MINUTES-";

    //二十四小时内
    public static final String WITHIN_TWENTY_FOUR_HOURS =  "WITHIN_TWENTY_FOUR_HOURS-";


    //24小时
    public static final Long TWENTY_FOUR_HOURS_UNIT_SECOND = 60 * 60 * 24L;

    /**
     * 设置5天缓存
     */
    public static final long FIVE_DAY = 60 * 60 * 24L * 5;

    //一
    public static final Long ONE_LONG = 1L;

    //5分钟
    public static final Long FIVE_MINUTES = 5 * 60L;

    //10分钟
    public static final Long TEN_MINUTES = 10 * 60L;

    //30秒
    public static final Long THIRTY_SECOND = 30L;


    //0次
    public static final int ZERO = 0;

    //10次
    public static final Long TEN = 10L;

    //20次
    public static final Long TWENTY = 20L;

    //50次
    public static final Long FIFTY = 50L;

    //昨日用户数量
    public static final String YESTERDAY_USER_NUMBER =  "YESTERDAY_USER_NUMBER";


    //用户中心用户全部信息键
    public static final String YHD_SERVICE_BUC_USER_ALL_INFO =  "YHD_SERVICE_BUC:USER_ALL_INFO:";

    //用户中心基本信息键
    public static final String YHD_SERVICE_BUC_USER_BASE_INFO =  "YHD_SERVICE_BUC:USER_BASE_INFO:";

    //用户中心用户角色键
    public static final String YHD_SERVICE_BUC_USER_ROLE_INFO =  "YHD_SERVICE_BUC:USER_ROLE_INFO:";

    //用户中心平台资源键
    public static final String YHD_SERVICE_BUC_PLATFORM_RESOURCES =  "YHD_SERVICE_BUC:PLATFORM_RESOURCES:";

    //用户中心资源控制键
    public static final String YHD_SERVICE_BUC_RESOURCES_CONTROL =  "YHD_SERVICE_BUC:RESOURCES_CONTROL:";

    //用户中心线上企业键
    public static final String YHD_SERVICE_BUC_ONLINE_COMPANY_INFO =  "YHD_SERVICE_BUC:ONLINE_COMPANY_INFO:";

    //用户中心线下企业键
    public static final String YHD_SERVICE_BUC_OFFLINE_COMPANY_INFO =  "YHD_SERVICE_BUC:OFFLINE_COMPANY_INFO:";

    //用户中心线下企业键V1(新键)
    public static final String YHD_SERVICE_BUC_OFFLINE_COMPANY_INFO_V1 =  "YHD_SERVICE_BUC:OFFLINE_COMPANY_INFO_V1:";



    //用户中心API键
    public static final String YHD_SERVICE_BUC_API_LIST =  "YHD_SERVICE_BUC:AUTH_API_LIST:";

    //平台编码集合
    public static final String YHD_SERVICE_BUC_PLATFORM_CODE_LIST =  "YHD_SERVICE_BUC:PLATFORM_CODE_LIST:";


    //用户中心线下平台关联键
    public static final String YHD_SERVICE_BUC_CRM_PLATFORM_RELATION =  "YHD_SERVICE_BUC:CRM_PLATFORM_RELATION:";



    /**
     * redis中的值的key
     */
    public static final String REGION_REDIS_LIST_VALUE = "YHD_SERVICE_BUC:REGION_LIST:";

}
