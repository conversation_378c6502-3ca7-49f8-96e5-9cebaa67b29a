package com.yhd.buc.foreign.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 * @version Id:LinkRequestVO.java, v 0.12023/3/6 11:20 kangrong Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "链接集合")
public class LinkRequestVO extends BaseVO {
    @Schema(description = "链接名称")
    private String linkName;

    @Schema(description = "链接URL")
    private String linkUrl;
}
