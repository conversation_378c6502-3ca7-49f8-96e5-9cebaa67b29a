package com.yhd.buc.foreign.pojo.vo.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.yhd.common.pojo.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id:QXBFuzzySearchVO.java, v 0.12022/7/5 11:30 kangrong Exp $
 * 启信宝模糊查询返回VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QXBFuzzySearchRequestVO extends BaseVO{
    //返回结果状态
    private String status;

    //返回结果消息
    private String message;

    //数据签名
    private String sign;

    private EnterprisePage data;

    /**
     * 企业类分页
     * */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public class EnterprisePage extends BaseVO{
        //返回列表中记录总数
        private Integer total;

        //当前返回的条目数
        private Integer num;

        private List<Enterprise> items;

        /**
         * 企业类
         * */
        @Data
        @EqualsAndHashCode(callSuper = true)
        public class Enterprise extends BaseVO {
            //企业名称
            private String name;

            //企业编号
            private String id;

            //成立日期
            @JSONField(name = "start_date")
            private String startDate;

            //企业法定代表人
            @JSONField(name = "oper_name")
            private String operName;

            //注册号
            @JSONField(name = "reg_no")
            private String regNo;

            //社会统一信用代码
            @JSONField(name = "credit_no")
            private String creditNo;

            //匹配关键字
            private String matchItems;

            //匹配类型
            private String matchType;

            //企业类型，枚举值：0 企业，4社团，5律所，6香港公司
            private String type;
        }
    }
}
