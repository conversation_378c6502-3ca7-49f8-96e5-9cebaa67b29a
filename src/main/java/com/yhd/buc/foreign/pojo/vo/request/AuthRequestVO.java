package com.yhd.buc.foreign.pojo.vo.request;

import com.yhd.buc.foreign.constant.RouteConstant;
import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version Id:RequestUserCompanyCodeVO.java, v 0.12022/7/26 9:03 kangrong Exp $
 * 用户权限VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "用户权限参数",required = true)
public class AuthRequestVO extends BaseVO {
    //用户编码
    @NotEmpty(message = RouteConstant.NOT_NULL_MESSAGE_TEMPLATE)
    @Schema(description = "用户编码",required = true)
    private String userCode;


    //平台标识
    @NotEmpty(message = RouteConstant.NOT_NULL_MESSAGE_TEMPLATE)
    @Schema(description = "平台标识",required = true)
    private String platformCode;

}
