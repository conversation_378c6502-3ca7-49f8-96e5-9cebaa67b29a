package com.yhd.buc.foreign.pojo.vo.response;
import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * uc_resources
 * <AUTHOR> 平台资源表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "返回资源权限")
public class ResourcesAuthResponseVO extends BaseVO {

    /**
     * 资源名称
     */
    @Schema(description = "资源名称")
    private String resourcesName;

    /**
     * 资源编码
     */
    @Schema(description = "资源编码")
    private String resourcesCode;

    /**
     * 状态
     */
    @Schema(description = "状态 enabled 启用 、disabled 禁用")
    private String status;
    
}