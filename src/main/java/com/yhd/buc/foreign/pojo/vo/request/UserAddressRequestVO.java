package com.yhd.buc.foreign.pojo.vo.request;

import com.yhd.buc.foreign.constant.CommonConstant;
import com.yhd.buc.foreign.constant.RouteConstant;
import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version Id:UserAddressRequestVO.java, v 0.12022/6/20 17:05 kangrong Exp $
 * 用户地址VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "用户地址VO")
public class UserAddressRequestVO extends BaseVO {
    @Schema(description = "ID(更新时必传)")
    private String id;

    //用户编码
    @NotBlank(message = RouteConstant.NOT_NULL_MESSAGE_TEMPLATE)
    @Schema(description = "用户编码",required = true)
    private String userCode;

    //联系人
    @NotBlank(message = RouteConstant.NOT_NULL_MESSAGE_TEMPLATE)
    @Size(max = 50,message = RouteConstant.LENGTH_MESSAGE_MESSAGE_TEMPLATE)
    @Schema(description = "联系人",required = true)
    private String contact;

    //联系电话
    @NotBlank(message = RouteConstant.NOT_NULL_MESSAGE_TEMPLATE)
    @Schema(description = "联系电话",required = true)
    private String contactNumber;

    //省ID
    @NotBlank(message = RouteConstant.NOT_NULL_MESSAGE_TEMPLATE)
    @Schema(description = "省ID",required = true)
    @Size(max = 20,message = RouteConstant.LENGTH_MESSAGE_MESSAGE_TEMPLATE)
    private String provinceId;

    //市ID
    @NotBlank(message = RouteConstant.NOT_NULL_MESSAGE_TEMPLATE)
    @Schema(description = "市ID",required = true)
    @Size(max = 20,message = RouteConstant.LENGTH_MESSAGE_MESSAGE_TEMPLATE)
    private String cityId;

    //镇ID
    @NotBlank(message = RouteConstant.NOT_NULL_MESSAGE_TEMPLATE)
    @Schema(description = "镇ID",required = true)
    @Size(max = 20,message = RouteConstant.LENGTH_MESSAGE_MESSAGE_TEMPLATE)
    private String townId;

    //省名称
    @NotBlank(message = RouteConstant.NOT_NULL_MESSAGE_TEMPLATE)
    @Schema(description = "省名称",required = true)
    @Size(max = 50,message = RouteConstant.LENGTH_MESSAGE_MESSAGE_TEMPLATE)
    private String province;

    //市
    @NotBlank(message = RouteConstant.NOT_NULL_MESSAGE_TEMPLATE)
    @Schema(description = "市名称",required = true)
    @Size(max = 50,message = RouteConstant.LENGTH_MESSAGE_MESSAGE_TEMPLATE)
    private String city;

    //镇
    @NotBlank(message = RouteConstant.NOT_NULL_MESSAGE_TEMPLATE)
    @Schema(description = "镇名称",required = true)
    @Size(max = 50,message = RouteConstant.LENGTH_MESSAGE_MESSAGE_TEMPLATE)
    private String town;

    //详细地址
    @NotBlank(message = RouteConstant.NOT_NULL_MESSAGE_TEMPLATE)
    @Schema(description = "详细地址",required = true)
    @Size(max = 255,message = RouteConstant.LENGTH_MESSAGE_MESSAGE_TEMPLATE)
    private String fullAddress;

    //默认状态
    @NotBlank(message = RouteConstant.NOT_NULL_MESSAGE_TEMPLATE)
    @Schema(description = "默认状态'true','false'",required = true)
    private String  defaultStatus;

    /**
     * 'invoice'(发票地址),'shipping'（收货地址）
     */
    @Schema(description = "发票类型 'invoice'(发票地址),'shipping'（收货地址）不填默认为收货地址")
    private String type;


    /**
     * 邮政编码
     */
    @Schema(description = "邮政编码")
    private Integer postalCode;
}
