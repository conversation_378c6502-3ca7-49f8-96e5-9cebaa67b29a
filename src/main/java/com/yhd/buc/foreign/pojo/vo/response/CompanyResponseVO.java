package com.yhd.buc.foreign.pojo.vo.response;
import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version Id:ResponseCompanyVO.java, v 0.12022/7/1 15:04 kangrong Exp $
 * 返回企业信息VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "返回企业信息参数")
public class CompanyResponseVO extends BaseVO {
    //企业编码
    @Schema(description = "企业编码")
    private String companyCode;

    //ERP企业编码
    @Schema(description = "ERP企业编码")
    private String erpCompanyCode;

    //企业名称
    @Schema(description = "企业名称")
    private String companyName;

    //联系人
    @Schema(description = "联系人")
    private String linkName;

    //联系电话
    @Schema(description = "联系电话")
    private String linkPhone;

    //企业地址
    @Schema(description = "企业地址")
    private String address;

    //是否测试企业
    @Schema(description = "是否测试企业")
    private String testCompany;


}
