package com.yhd.buc.foreign.pojo.vo.request;

import com.yhd.buc.foreign.constant.RouteConstant;
import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version Id:UserAddressCodeRequestVO.java, v 0.12022/6/20 17:16 kangrong Exp $
 * 用户地址
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "用户地址参数VO")
public class UserAddressCodeRequestVO extends BaseVO {
    //用户编码
    @NotEmpty(message = RouteConstant.NOT_NULL_MESSAGE_TEMPLATE)
    @Schema(description = "用户编码",required = true)
    private  String userCode;

    //地址id
    @NotEmpty(message = RouteConstant.NOT_NULL_MESSAGE_TEMPLATE)
    @Schema(description = "地址id",required = true)
    private  String addressId;

    @Schema(description = "地址类型，invoice'(发票地址),'shipping'（收货地址） ，不填默认为收货地址")
    private String type;
}
