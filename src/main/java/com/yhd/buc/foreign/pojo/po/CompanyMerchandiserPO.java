package com.yhd.buc.foreign.pojo.po;


import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 业务员专属跟单关系表(CompanyMerchandiser)实体类
 *
 * <AUTHOR>
 * @since 2023-03-13 11:58:38
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "uc_company_merchandiser")
public class CompanyMerchandiserPO extends BaseEntity {

    /**
     * 平台标识
     */
    private String platformCode;

    /**
     * 企业编码
     */
    private String companyCode;
    /**
     * 业务员工号
     */
    private String salesmanCode;
    /**
     * 跟单员工号
     */
    private String merchandiserCode;



}

