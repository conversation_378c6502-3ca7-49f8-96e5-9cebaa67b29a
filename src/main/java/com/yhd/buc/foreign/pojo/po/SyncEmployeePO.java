package com.yhd.buc.foreign.pojo.po;


import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (SyncEmployee)实体类
 *
 * <AUTHOR>
 * @since 2023-03-10 16:50:46
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "uc_sync_employee")
public class SyncEmployeePO extends BaseEntity {

    /**
     * 员工姓名
     */
    private String employeeName;
    /**
     * 工号
     */
    private String employeeCode;
    /**
     * 部门编号
     */
    private String unitCode;
    /**
     * 部门名称
     */
    private String unitName;

    /**
     * 'normal'（正常）,
     * 'dimission',（离职）'suspension',*（停职）'caller'（访客）,'operator'（操作员）
     */
    private String status;

    /**
     * 手机号
     */
    private String mobile;


    /**
     * 公司手机号
     */
    private String companyMobile;

}

