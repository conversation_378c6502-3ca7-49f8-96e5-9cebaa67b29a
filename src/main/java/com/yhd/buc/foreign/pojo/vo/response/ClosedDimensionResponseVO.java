package com.yhd.buc.foreign.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id:ResponseAuthorityVO.java, v 0.12022/7/20 16:06 kangrong Exp $
 * 关闭的资源
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "关闭的资源")
public class ClosedDimensionResponseVO extends BaseVO {
    //API权限列表
    @Schema(description = "关闭的资源API权限列表")
    private List<String> authApiList;

    //权限值列表
    @Schema(description = "关闭的资源权限值列表")
    private List<String> authValueList;

}
