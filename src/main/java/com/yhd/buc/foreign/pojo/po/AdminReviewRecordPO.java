package com.yhd.buc.foreign.pojo.po;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * uc_admin_review_record
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "uc_admin_review_record")
public class AdminReviewRecordPO extends BaseEntity {
    /**
     * 主键
     */
    private String id;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 用户当前角色
     */
    private String userRole;

    /**
     *'wait'(待审核),'pass'（审核通过）,'refuse'（拒绝）
     * */
    private String status;
}