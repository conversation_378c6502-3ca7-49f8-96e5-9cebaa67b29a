package com.yhd.buc.foreign.pojo.vo.response;


import com.yhd.common.pojo.vo.BaseVO;
import lombok.*;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version Id: SmartAddressResponse.java, v 0.1 2025/7/21 09:46 JiangYuHong Exp $
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SmartAddressResponse extends BaseVO {

    /**
     * 身份证号
     */
    private String idNumber;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 邮编
     */
    private String postCode;

    /**
     * 姓名
     */
    private String name;

    /**
     * 地址
     */
    private String address;

    /**
     * 省份
     */
    private String province;

    /**
     * 省份id
     */
    private int provinceId;

    /**
     * 城市
     */
    private String city;

    /**
     * 城市id
     */
    private int cityId;

    /**
     * 区县
     */
    private String region;

    /**
     * 区县id
     */
    private int regionId;

    /**
     * 街道
     */
    private String street;
}
