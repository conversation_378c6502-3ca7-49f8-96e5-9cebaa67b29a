package com.yhd.buc.foreign.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * @version Id: RegionRequestVO.java, v0.1 2022/8/12 15:19 yehuasheng Exp $
 */
@Schema(description = "根据地区编码获取夏季的地区信息请求参数")
@EqualsAndHashCode(callSuper = true)
@Data
public class RegionRequestVO extends BaseVO {
    /**
     * 地区的编码id
     */
    @Schema(description = "地区的编码id")
    private String regionId = "0";
}
