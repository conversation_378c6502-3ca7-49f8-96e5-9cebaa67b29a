package com.yhd.buc.foreign.pojo.vo.response;

import com.yhd.common.pojo.po.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version Id:MerchandiserResponseVO.java, v 0.12024/7/15 14:55 kangrong Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "用户管理员列表")
public class MerchandiserResponseVO extends BaseEntity {
    //跟单员工号
    @Schema(description = "跟单员工号")
    private String merchandiserJobNumber;

    //跟单员名称
    @Schema(description = "跟单员名称")
    private String merchandiserName;

    //跟单员电话
    @Schema(description = "跟单员电话")
    private String merchandiserMobile;
}
