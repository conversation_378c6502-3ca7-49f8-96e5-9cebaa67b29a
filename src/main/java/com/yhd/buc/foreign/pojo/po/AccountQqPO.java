package com.yhd.buc.foreign.pojo.po;


import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * QQ账户表(AccountQq)实体类
 *
 * <AUTHOR>
 * @since 2023-02-23 17:33:06
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "uc_account_qq")
public class AccountQqPO extends BaseEntity {

    /**
     * 用户编码（已注册用户才填写）
     */
    private String userCode;
    /**
     * QQ用户名
     */
    private String userName;
    /**
     * QQ第三方登录授权的openID
     */
    private String qqOpenid;
    /**
     * 状态：disabled：禁用；enabled：启用
     */
    private String status;


}

