package com.yhd.buc.foreign.pojo.vo.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.yhd.common.pojo.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id:QXBPreciseVO.java, v 0.12022/7/5 10:07 kangrong Exp $
 * 调用启信宝精确查询返回VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QXBPreciseRequestVO extends BaseVO {
    //返回结果状态
    private String status;

    //返回结果消息
    private String message;

    //数据签名
    private String sign;

    @JSONField(name = "data")
    private EnterpriseDetails enterpriseDetailsDTO;

    /**
     * 企业详情类
     * */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public class EnterpriseDetails extends BaseVO{
        //企业id
        private String id;

        //企业名称（国家公示）
        private String name;

        //标准企业名称（清洗名称）
        @JSONField(name = "format_name")
        private String formatName;

        //企业类型
        private String econKind;

        //企业类型代码
        private String econKindCode;

        //注册资本（金额数字+1个空格+万+货币单位）
        private String registCapi;

        //货币单位
        @JSONField(name = "currency_unit")
        private String currencyUnit;

        //企业大类
        //01- 大陆企    02- 社会组织   03- 机关及事业单位  04- 港澳台及国外企业  05- 律所及其他组织机构
        @JSONField(name = "type_new")
        private String typeNew;

        //企业二级分类0115601:企业;0115602:个体;0115603:农民专业合作社;0115699:其他类型0215601
        private String categoryNew;

        //历史名称
        @JSONField(name = "historyNames")
        private List<String> historyName;

        //地址
        private String address;

        //企业注册号
        private String regNo;

        //经营范围
        private String scope;

        //营业开始日期
        private String termStart;

        //营业结束日期
        private String termEnd;

        //所属工商局
        private String belongOrg;

        //企业法定代表人
        private String operName;

        //成立日期
        private String startDate;

        //注销日期
        private String endDate;

        //核准日期
        private String checkDate;

        //经营状态（清洗后状态）
        //1：存续；2：注销；3：吊销；4：撤销，5：迁出，6：设立中，7：清算中，8：停业，9：其他
        @JSONField(name = "new_status")
        private String newStatus;

        //组织机构号
        private String orgNo;

        //统一社会信用代码
        private String creditNo;

        //地区代码
        private String districtCode;

        //实缴资本
        private String actualCapi;

        //四级行业
        private String domain;

        //1-新三板；6-主板上市公司；40-暂停上市；41-终止上市；/9-香港上市；17-高新企业；
        @JSONField(name = "tags")
        private List<String> tags;

        //吊销原因
        @JSONField(name = "revoke_reason")
        private String revokeReason;

        //吊销日期
        @JSONField(name = "revoke_date")
        private String revokeDate;

        //公司代表人职务
        private String title;

    }

}
