package com.yhd.buc.foreign.pojo.vo.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version Id:ResponseUserInfoVO.java, v 0.12022/6/22 17:53 kangrong Exp $
 * 用户管理员列表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "用户管理员列表")
public class UserInfoByUserNameResponseVO extends BaseVO {
    //用户编码
    @Schema(description = "用户编码")
    private  String  userCode;

    //用户名
    @Schema(description = "用户名")
    private  String  userName;

}
