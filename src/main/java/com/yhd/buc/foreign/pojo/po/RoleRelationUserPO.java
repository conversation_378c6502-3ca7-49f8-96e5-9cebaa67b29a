package com.yhd.buc.foreign.pojo.po;


import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 角色-用户关联表(RoleRelationUser)实体类
 *
 * <AUTHOR>
 * @since 2023-02-28 21:07:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "uc_role_relation_user")
public class RoleRelationUserPO extends BaseEntity {

    /**
     * 角色编码
     */
    private String roleCode;
    /**
     * 用户code
     */
    private String userCode;
    /**
     * 平台标识
     */
    private String platformCode;



}

