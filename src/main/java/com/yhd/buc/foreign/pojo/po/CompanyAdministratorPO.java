package com.yhd.buc.foreign.pojo.po;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * uc_company_administrator
 * <AUTHOR> 企业管理员表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "uc_company_administrator")
public class CompanyAdministratorPO extends BaseEntity {
    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 企业编码
     */
    private String companyCode;
}