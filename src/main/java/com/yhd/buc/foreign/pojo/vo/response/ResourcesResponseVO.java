package com.yhd.buc.foreign.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version Id:ResourcesResponseVO.java, v 0.12023/3/22 11:43 kangrong Exp $
 * 资源VO
 */

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "资源VO")
public class ResourcesResponseVO extends BaseVO {
    private String id;


    /**
     * 配置编码
     */
    private String configId;

    /**
     * 资源名称
     */
    @Schema(description = "资源名称")
    private String resourcesName;

    /**
     * 资源路径
     */
    @Schema(description = "资源路径")
    private String resourcesPath;


    /**
     * 资源编码
     */
    @Schema(description = "资源编码")
    private String resourcesCode;

}
