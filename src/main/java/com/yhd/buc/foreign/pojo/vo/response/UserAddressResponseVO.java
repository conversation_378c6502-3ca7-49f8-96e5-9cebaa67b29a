package com.yhd.buc.foreign.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version Id:UserAddressResponseVO.java, v 0.12022/6/21 15:34 kangrong Exp $
 * 返回用户地址VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "返回用户地址VO")
public class UserAddressResponseVO extends BaseVO {
    @Schema(description = "IdVO")
    private String id;
    //联系人
    @Schema(description = "联系人")
    private String contact;

    //联系电话
    @Schema(description = "联系电话")
    private String contactNumber;

    //省ID
    @Schema(description = "省ID")
    private String provinceId;

    //市ID
    @Schema(description = "市ID")
    private String cityId;

    //镇ID
    @Schema(description = "镇ID")
    private String townId;

    //省
    @Schema(description = "省")
    private String province;

    //市
    @Schema(description = "市")
    private String city;

    //镇
    @Schema(description = "镇")
    private String town;

    //详细地址
    @Schema(description = "详细地址")
    private String fullAddress;

    //邮政编码
    @Schema(description = "邮政编码")
    private String postalCode;

    //默认状态
    @Schema(description = "默认状态")
    private String defaultStatus;

    //默认状态
    @Schema(description = "异常状态'true 是','false 否'")
    private String exceptionStatus;


}
