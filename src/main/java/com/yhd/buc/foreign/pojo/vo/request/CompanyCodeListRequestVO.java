package com.yhd.buc.foreign.pojo.vo.request;

import com.yhd.buc.foreign.constant.RouteConstant;
import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id:RequestCompanyVO.java, v 0.12022/7/2 10:14 kangrong Exp $
 * 企业编码集合VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CompanyCodeListRequestVO extends BaseVO {
    //企业编码
    @Schema(description = "企业编码集合",required = true)
    private List<String> companyCodeList;

    //平台标识
    @NotEmpty(message = RouteConstant.NOT_NULL_MESSAGE_TEMPLATE)
    @Schema(description = "平台标识",required = true)
    private String platformCode;
}
