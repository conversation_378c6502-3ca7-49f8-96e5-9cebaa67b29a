package com.yhd.buc.foreign.pojo.po;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户信息表
 * 
 * <AUTHOR>
 * @since 2022-06-28
 */

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("uc_user_info")
public class UserInfoPO extends BaseEntity {
    

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 用户名称
     */
    @TableField("username")
    private String username;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 部门
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED )
    private String department;

    /**
     * 职位
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED )
    private String occupation;

    /**
     * 性别：female：女；male：男；secrecy：保密
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED )
    private String gender;

    /**
     * 生日
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED )
    private String birthday;

    /**
     * 兴趣爱好
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED )
    private String hobby;

    /**
     * 头像
     */
    private String avatarPath;


    /**
     * 'enabled'(启用),'disabled'（未启用）
     */
    private String status;


    /**
     * 'false','true'
     */
    private String isTest;

    /**
     * 'admin'(后台注册),'invite'（邀请注册）,'normal'（正常注册）
     */
    private String businessSource;

    /**
     * pc电脑端,wap手机端
     */
    private String registerSource;

    /**
    * 注册平台来源
    * */
    private String platformSource;

}