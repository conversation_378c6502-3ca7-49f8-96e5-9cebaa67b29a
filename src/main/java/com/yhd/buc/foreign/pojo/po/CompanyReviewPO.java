package com.yhd.buc.foreign.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * uc_company_review
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "uc_company_review")
public class CompanyReviewPO extends BaseEntity {

    /**
     * 申请的类型('ordinary','authentication'普通企业、认证企业)
     */
    private String type;

    /**
     * 企业名字
     */
    private String companyName;

    /**
     * 平台标识
     */
    private String platformCode;

    /**
     * ERP企业编码
     */
    private String erpCompanyCode;

    /**
     * 营业执照存放地址
     */
    private String businessLicenseUrl;

    /**
     * 申请表地址
     */
    private String applicationFormUrl;

    /**
     * 企业地址
     */
    private String address;

    /**
     * 详细地址
     */
    private String fullAddress;

    /**
     * 省ID
     */
    private String provinceId;

    /**
     * 市ID
     */
    private String cityId;

    /**
     * 镇ID
     */
    private String townId;

    /**
     * 联系方式
     */
    private String linkphone;

    /**
     * 职位
     */
    private String position;

    /**
     * 联系人
     */
    private String linkname;

    /**
     * 联系人邮箱
     */
    private String linkEmail;

    /**
     * 创建人用户编码
     */
    private String submitUserCode;

    /**
     * 审核人工号
     */
    private String emplCode;

    /**
     * 审核状态：unreviewed:待审核；passed:审核通过；failure:审核不过；return:取消申请
     */
    private String reviewStatus;

    /**
     * 审核备注
     */
    private String remark;

    /**
     * 审核不过类型
     */
    private String refuseType;

    /**
     * 拒绝或退回修改的原因
     */
    private String refuseReason;

    /**
     * 审核时间
     */
    private LocalDateTime refuseDate;


    /**
     * 'true'(已查看),'false'（未查看）
     */
    private String checkStatus;

    /**
     * 部门
     */
    private String department;

}