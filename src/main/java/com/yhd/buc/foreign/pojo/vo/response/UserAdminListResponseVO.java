package com.yhd.buc.foreign.pojo.vo.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version Id:ResponseUserInfoVO.java, v 0.12022/6/22 17:53 kangrong Exp $
 * 用户管理员列表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "用户管理员列表")
public class UserAdminListResponseVO extends BaseVO {
    private  String  id;
    //用户编码
    @Schema(description = "用户编码")
    private  String  userCode;

    //用户名
    @Schema(description = "用户名")
    private  String  username;

    //邮箱
    @JsonInclude
    @Schema(description = "邮箱")
    private  String  email;

    //邮箱是否绑定
    @JsonInclude
    @Schema(description = "邮箱是否绑定")
    private String emailCheck;

    //手机是否绑定
    @JsonInclude
    @Schema(description = "手机是否绑定")
    private String mobileCheck;

    //手机
    @JsonInclude
    @Schema(description = "手机")
    private  String  mobile;

    //职位
    @JsonInclude
    @Schema(description = "职位")
    private  String  occupation;

    //是否是管理员
    @Schema(description = "是否是管理员 true是 false否")
    private  String  admin = "false";

}
