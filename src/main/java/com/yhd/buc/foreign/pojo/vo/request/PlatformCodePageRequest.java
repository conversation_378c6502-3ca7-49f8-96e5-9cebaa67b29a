package com.yhd.buc.foreign.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import com.yhd.common.pojo.vo.PageRequestVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version Id:PlatformCodeRequest.java, v 0.12023/3/17 18:54 kangrong Exp $
 * 根据平台获取用户编辑集合
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "平台编码请求")
public class PlatformCodePageRequest extends PageRequestVO {
    @NotEmpty(message = "平台编码不能为空")
    @Schema(description = "平台编码",required = true)
    private String platformCode;
}
