package com.yhd.buc.foreign.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * uc_com_home
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "uc_com_home")
public class ComHomePO extends BaseEntity {
    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 样式名称
     */
    private String styleName;

    /**
     * 样式内容
     */
    private String styleContent;

    /**
     * 状态 'enabled'启用,'disabled'禁用,'deleted'删除
     * */
    private String status;


    /**
     * 联系电话
     */
    private String contactNumber;

    /**
     * 联系跳转链接
     */
    private String contactUrl;

    /**
     * 文本文档url（json格式）
     */
    private String textDocumentUrl;

    /**
     * 友情链接（json格式）
     */
    private String friendshipLink;

    /**
     * 版权信息
     */
    private String copyrightInformation;

    /**
     * 营业执照
     */
    private String businessLicense;

    /**
     * 营业执照url
     */
    private String businessLicenseUrl;

    /**
     * 底部链接（json格式）
     */
    private String bottomLink;

    /**
     * logo图片
     */
    private String logoPicture;

    /**
     * logo链接
     */
    private String logoUrl;

    /**
     * 登录注册背景图
     */
    private String loginRegistrationPicture;

    /**
     * 登录注册链接
     */
    private String loginRegistrationLink;

    /**
     * 主题色
     */
    private String themeColor;
}