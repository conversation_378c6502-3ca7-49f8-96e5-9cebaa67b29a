package com.yhd.buc.foreign.pojo.po;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * uc_resources
 * <AUTHOR> 平台资源表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "uc_resources")
public class ResourcesPO extends BaseEntity {

    /**
     * 资源名称
     */
    private String resourcesName;

    /**
     * 资源路径
     */
    private String resourcesPath;

    /**
     * 配置标识
     */
    private String configId;

    /**
     * 资源类型id
     */
    private String resourcesTypeId;

    /**
     * 备注说明
     */
    private String remark;


    /**
     * 资源编码
     */
    private String resourcesCode;

    /**
     * 状态
     */
    private String status;
    
}