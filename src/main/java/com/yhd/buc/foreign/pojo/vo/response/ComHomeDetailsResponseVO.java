package com.yhd.buc.foreign.pojo.vo.response;

import com.yhd.buc.foreign.pojo.vo.request.LinkRequestVO;
import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * uc_com_home
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "主页配置详情参数")
public class ComHomeDetailsResponseVO extends BaseVO {
    @Schema(description = "ID",required = true)
    private String id;
    /**
     * 平台编码
     */
    @Schema(description = "平台编码",required = true)
    private String platformCode;

    /**
     * 平台名称
     */
    @Schema(description = "平台名称",required = true)
    private String platformName;

    /**
     * 样式
     */
    @Schema(description = "样式名称",required = true)
    private String styleName;

    /**
     * 样式内容
     */
    @Schema(description = "样式内容",required = true)
    private String styleContent;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话",required = true)
    private String contactNumber;

    /**
     * 联系跳转链接
     */
    @Schema(description = "联系跳转链接",required = true)
    private String contactUrl;

    /**
     * 文本文档集合
     */
    @Schema(description = "文本文档集合",required = true)
    private List<LinkRequestVO> textDocumentList;

    /**
     * 友情链接（json格式）
     */
    @Schema(description = "友情链接集合")
    private List<LinkRequestVO> friendshipList;

    /**
     * 版权信息
     */
    @Schema(description = "版权信息",required = true)
    private String copyrightInformation;

    /**
     * 营业执照
     */
    @Schema(description = "营业执照",required = true)
    private String businessLicense;

    /**
     * 营业执照url
     */
    @Schema(description = "营业执照url",required = true)
    private String businessLicenseUrl;

    /**
     * 底部链接（json格式）
     */
    @Schema(description = "底部链接")
    private List<LinkRequestVO> bottomLinkList;

    /**
     * logo图片
     */
    @Schema(description = "logo图片",required = true)
    private String logoPicture;

    /**
     * logo链接
     */
    @Schema(description = "logo链接",required = true)
    private String logoUrl;

    /**
     * 登录注册背景图
     */
    @Schema(description = "登录注册背景图",required = true)
    private String loginRegistrationPicture;

    /**
     * 登录注册链接
     */
    @Schema(description = "登录注册链接")
    private String loginRegistrationLink;

    /**
     * 主题色
     */
    @Schema(description = "主题色")
    private String themeColor;
}