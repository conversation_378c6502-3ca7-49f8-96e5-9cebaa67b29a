package com.yhd.buc.foreign.pojo.po;



import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * uc_platform
 * <AUTHOR> 平台
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "uc_platform")
public class PlatformPO extends BaseEntity {

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 平台描述
     */
    private String description;

    /**
     * 首页地址
     */
    private String homePage;

    /**
     * 个人中心
     */
    private String userCenter;

    /**
     * 接入资源id，多个用逗号分割,正常情况只需要网关接入就可以了
     */
    private String resourceIds;

    /**
     * 客户端访问密匙
     */
    private String clientSecret;

    /**
     * 客户端申请的权限范围
     */
    private String scope;

    /**
     * 指定客户端支持的grant_type,可选值包括authorization_code,password,refresh_token,implicit,client_credentials, 若支持多个grant_type用逗号(,)分隔,
     */
    private String authorizedGrantTypes;

    /**
     * 回调地址
     */
    private String redirectUri;

    /**
     * 'enabled'(启用),'disabled'（未启用）
     */
    private String status;

    /**
     * token有效期(单位秒)
     */
    private Integer accessTokenValidity;

    /**
     * refreshToken有效期(单位秒)
     */
    private Integer refreshTokenValidity;

    /**
     * 是否自动Approval操作
     */
    private String autoapprove;

    /**
     * 子公司标识(当前平台所属公司,例:东莞、苏州、工品)
     */
    private String companyCode;

}