package com.yhd.buc.foreign.pojo.po;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * uc_shipping_address
 * <AUTHOR>
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "uc_shipping_address")
public class ShippingAddressPO extends BaseEntity {
    /**
     * 用户标识
     */
    private String userCode;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 联系电话
     */
    private String contactNumber;

    /**
     * 省ID
     */
    private String provinceId;

    /**
     * 省名
     */
    private String province;

    /**
     * 市ID
     */
    private String cityId;

    /**
     * 市名
     */
    private String city;

    /**
     * 县区ID
     */
    private String townId;

    /**
     * 县区名
     */
    private String town;

    /**
     * 详细地址
     */
    private String fullAddress;

    /**
     * 邮政编码
     */
    private Integer postalCode;

    /**
     * 是否默认
     */
    private String defaultStatus;

    /**
     * 'invoice'(发票地址),'shipping'（收货地址）
     */
    private String type;

    /**
     * 状态'enabled','disabled'
     */
    private String status;


    /**
     * 异常状态（省市区编码异常的）'true','false'
     */
    private String exceptionStatus;

}