package com.yhd.buc.foreign.pojo.vo.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version Id:ResponseAuthorityVO.java, v 0.12022/7/20 16:06 kangrong Exp $
 * 返回的角色信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "返回的角色信息")
public class RoleResponseVO extends BaseVO {
    //用户编码
    @Schema(description = "用户编码")
    private String userCode;

    //平台标识
    @Schema(description = "平台标识")
    private String platformCode;

    //角色名称
    @Schema(description = "角色名称")
    private String roleName;

    //用户角色值
    @Schema(description = "用户角色值")
    private String userRole;

    //企业角色值
    @JsonInclude
    @Schema(description = "企业角色值")
    private String companyRole;

    //企业角色名称
    @Schema(description = "企业角色名称")
    private String companyRoleName;


    //认证状态
    @Schema(description = "企业认证状态 true 通过认证 false 未认证")
    private String authenticationStatus;

}
