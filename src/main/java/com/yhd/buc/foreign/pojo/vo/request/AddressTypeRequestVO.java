package com.yhd.buc.foreign.pojo.vo.request;
import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version Id:PageUserVO.java, v 0.12022/6/20 17:13 kangrong Exp $
 * 分页VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "类型参数")
public class AddressTypeRequestVO extends BaseVO {
    //"用户编码
    @Schema(description = "地址类型，invoice'(发票地址),'shipping'（收货地址） ，不填默认为收货地址")
    private  String type;

    //"用户编码
    @NotEmpty(message = "用户编码不能为空")
    @Schema(description = "用户编码",required = true)
    private  String userCode;
}
