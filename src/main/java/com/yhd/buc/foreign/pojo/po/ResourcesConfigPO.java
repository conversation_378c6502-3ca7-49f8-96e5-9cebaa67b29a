package com.yhd.buc.foreign.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * uc_resources_config
 * <AUTHOR> 平台资源配置表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "uc_resources_config")
public class ResourcesConfigPO extends BaseEntity {

    /**
     * 资源名称
     */
    private String configName;

    /**
     * 平台标识
     */
    private String platformCode;

    /**
     * 资源说明
     */
    private String remark;

    /**
     * 状态'enabled','disabled'
     */
    private String status;

}