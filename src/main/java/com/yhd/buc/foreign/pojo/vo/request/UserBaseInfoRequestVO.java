package com.yhd.buc.foreign.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version Id:RequestUserInfoVO.java, v 0.12022/6/22 16:32 kangrong Exp $
 * 用户基础信息VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "用户基础信息参数")
public class UserBaseInfoRequestVO extends BaseVO {
    //用户编码
    @NotEmpty(message = "用户编码不能为空")
    @Schema(description = "用户编码",required = true)
    private  String userCode;

    //用户名称
    @NotEmpty(message = "用户名称不能为空不能为空")
    @Schema(description = "用户名称",required = true)
    private  String  userName;

    //职位
    @Schema(description = "职位")
    private  String  occupation;

    //性别：female：女；male：男；secrecy：保密
    @Schema(description = "性别：female：女；male：男；secrecy：保密")
    private  String  gender;

    //生日
    @Schema(description = "生日")
    private  String  birthday;

    //爱好
    @Schema(description = "爱好")
    private  String  hobby;

}
