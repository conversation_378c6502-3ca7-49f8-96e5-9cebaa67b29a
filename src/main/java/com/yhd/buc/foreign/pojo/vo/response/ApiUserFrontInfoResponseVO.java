package com.yhd.buc.foreign.pojo.vo.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @version Id:ResponseUserInfoVO.java, v 0.12022/6/22 17:53 kangrong Exp $
 * 返回全部用户信息VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "返回用户所有信息VO")
public class ApiUserFrontInfoResponseVO extends BaseVO {
    //用户编码
    @Schema(description = "用户编码")
    private  String  userCode;

    //用户名
    @Schema(description = "用户名")
    private  String  userName;

    //职位
    @JsonInclude
    @Schema(description = "职位")
    private  String  occupation;

    //性别：female：女；male：男；secrecy：保密
    @JsonInclude
    @Schema(description = "性别：female：女；male：男；secrecy：保密")
    private  String  gender;

    //生日
    @JsonInclude
    @Schema(description = "生日")
    private  String  birthday;

    //爱好
    @JsonInclude
    @Schema(description = "爱好")
    private  String  hobby;

    //邮箱
    @JsonInclude
    @Schema(description = "邮箱")
    private  String  email;

    //邮箱是否绑定
    @JsonInclude
    @Schema(description = "邮箱是否绑定")
    private String emailCheck;

    //手机是否绑定
    @JsonInclude
    @Schema(description = "手机是否绑定")
    private String mobileCheck;

    //手机
    @JsonInclude
    @Schema(description = "手机")
    private  String  mobile;

    //用户头像
    @Schema(description = "用户头像")
    private  String  avatarPath;

    //是否是测试账户
    @Schema(description = "是否是测试账户")
    private  String  testUser;

    //是否是管理员
    @Schema(description = "是否是管理员")
    private  String   admin;

    //企业编码
    @Schema(description = "企业编码")
    private String companyCode;

    //ERP企业编码
    @Schema(description = "ERP企业编码")
    private String erpCompanyCode;

    //企业角色值
    @JsonInclude
    @Schema(description = "企业角色值 yhd-fa-company-ren-zheng-qi-ye(普通企业)、yhd-fa-company-ren-zheng-qi-ye（认证企业）")
    private String companyRole;

    //企业名称
    @Schema(description = "企业名称")
    private String companyName;

    //联系人
    @Schema(description = "联系人")
    private String linkName;

    //联系电话
    @Schema(description = "联系电话")
    private String linkPhone;

    //企业地址
    @Schema(description = "企业地址")
    private String address;

    //是否测试企业
    @Schema(description = "是否测试企业")
    private String testCompany;

    //qq是否绑定
    @Schema(description = "qq是否绑定")
    private String qqCheck;

    //qq名字
    @Schema(description = "qq名字")
    private  String  qqName;

    //微信是否绑定
    @Schema(description = "微信是否绑定")
    private String wechatCheck;

    //微信名字
    @Schema(description = "微信名字")
    private  String  wechatName;


    //平台标识
    @Schema(description = "平台标识")
    private String platformCode;


    //用户角色值
    @Schema(description = "用户角色值 yhd-user-user-ge-ren-yong-hu（个人用户）yhd-user-user-pu-tong-qi-ye-yong-hu（普通企业用户）yhd-user-user-ren-zheng-qi-ye-yong-hu（认证企业用户）")
    private String userRole;


    @Schema(description = "企业状态normal(正常)、audit(认证审核)，filing建档 ，processing(加入企业中)")
    private String companyStatus;

    //认证状态
    @Schema(description = "企业认证状态 true 通过认证 false 未认证(默认为false)")
    private String authenticationStatus;

    @Schema(description = "资源权限集合")
    @JsonInclude
    private List<ResourcesAuthResponseVO> resourcesAuthResponseVOList;


    //跟单员工号
    @JsonInclude
    @Schema(description = "跟单员工号")
    private String merchandiserJobNumber;

    //跟单员名称
    @JsonInclude
    @Schema(description = "跟单员名称")
    private String merchandiserName;

    //跟单员电话
    @JsonInclude
    @Schema(description = "跟单员电话")
    private String merchandiserMobile;

    //跟单员邮箱
    @JsonInclude
    @Schema(description = "跟单员邮箱")
    private String merchandiserEmail;

    //跟单员QQ
    @JsonInclude
    @Schema(description = "跟单员QQ")
    private String merchandiserQq;

    //微信公众号是否绑定
    @Schema(description = "微信公众号是否绑定true 绑定 false 未绑定")
    private  String  wechatOfficialCheck;


    @Schema(description = "归属公司 东莞-DGYHD 苏州-SZYHD")
    @JsonInclude
    private String ownershipCompany;

    /**
     * 业务员工号
     */
    @Schema(description = "业务员工号")
    @JsonInclude
    private String operatorJobNumber;

    /**
     * 业务员名字
     */
    @Schema(description = "业务员名字")
    @JsonInclude
    private String operatorName;

    /**
     * 业务员手机
     */
    @Schema(description = "业务员手机")
    @JsonInclude
    private String operatorMobile;


    /**
     * 业务员邮箱
     */
    @Schema(description = "业务员邮箱")
    @JsonInclude
    private String operatorEmail;

    /**
     * 业务员qq
     */
    @Schema(description = "业务员qq")
    @JsonInclude
    private String operatorQq;

    //跟单员岗位
    @JsonInclude
    @Schema(description = "跟单员岗位")
    private String merchandiserPost;


    //业务员岗位
    @JsonInclude
    @Schema(description = "业务员岗位")
    private String operatorPost;

    @Schema(description = "注册时间")
    private LocalDateTime registerDate;

    @Schema(description = "折扣等级 A-1 B-2 C-3 D-4 B+-5 C+-6 D+-7 S-8（4 7 3 6 2 5 1 8从小到大）")
    @JsonInclude
    private int discountLevel;

    @Schema(description = "业务员企业微信二维码")
    @JsonInclude
    private String operatorEnterpriseWechatCodeUrl;

    @Schema(description = "跟单员企业微信二维码")
    @JsonInclude
    private String merchandiserEnterpriseWechatCodeUrl;
}
