package com.yhd.buc.foreign.pojo.po;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * uc_wechat_official
 * <AUTHOR> 微信公众号
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("uc_wechat_official")
public class WechatOfficialPO extends BaseEntity {
    /**
     * 用户编码（已注册用户才填写）
     */
    private String userCode;

    /**
     * openid
     */
    private String openid;

    /**
     * 用户统一标识。针对一个微信开放平台帐号下的应用，同一用户的unionid是唯一的。
     */
    private String unionId;

    /**
     * 平台编码
     */
    private String platformCode;
}