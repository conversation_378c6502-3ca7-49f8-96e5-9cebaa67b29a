package com.yhd.buc.foreign.pojo.vo.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.yhd.common.pojo.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id:QCCPreciseQueryVO.java, v 0.12022/2/24 11:10 kangrong Exp $
 * 企查查精确搜索VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QCCPreciseQueryRequestVO extends BaseVO {
    //返回状态码
    @JSONField(name = "Status")
    private String status;

    //返回结果集
    @JSONField(name = "Result")
    private List<EnterpriseDetails> result;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<EnterpriseDetails> getResult() {
        return result;
    }

    public void setResult(List<EnterpriseDetails> result) {
        this.result = result;
    }

    @Override
    public String toString() {
        return "QCCPreciseQueryVO{" +
                "status='" + status + '\'' +
                ", result=" + result +
                '}';
    }

    /**
     * 企业详情实体类
     * */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public class  EnterpriseDetails extends BaseVO{
        //维度
        @JSONField(name = "Dimension")
        private  String dimension;

        //匹配原因
        @JSONField(name = "MatchList")
        private  List<Match> matchList;

        //内部KeyNo
        @JSONField(name = "KeyNo")
        private  String keyNo;

        //公司名称
        @JSONField(name = "Name")
        private  String name;

        //法定代表人名称
        @JSONField(name = "OperName")
        private  String operName;

        //成立日期
        @JSONField(name = "StartDate")
        private  String startDate;

        //企业状态
        @JSONField(name = "Status")
        private  String status;

        //注册号
        @JSONField(name = "No")
        private  String no;

        //统一社会信用代码
        @JSONField(name = "CreditCode")
        private  String creditCode;

        public String getDimension() {
            return dimension;
        }

        public void setDimension(String dimension) {
            this.dimension = dimension;
        }

        public List<Match> getMatchList() {
            return matchList;
        }

        public void setMatchList(List<Match> matchList) {
            this.matchList = matchList;
        }

        public String getKeyNo() {
            return keyNo;
        }

        public void setKeyNo(String keyNo) {
            this.keyNo = keyNo;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getOperName() {
            return operName;
        }

        public void setOperName(String operName) {
            this.operName = operName;
        }

        public String getStartDate() {
            return startDate;
        }

        public void setStartDate(String startDate) {
            this.startDate = startDate;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getNo() {
            return no;
        }

        public void setNo(String no) {
            this.no = no;
        }

        public String getCreditCode() {
            return creditCode;
        }

        public void setCreditCode(String creditCode) {
            this.creditCode = creditCode;
        }

        @Override
        public String toString() {
            return "EnterpriseDetails{" +
                    "dimension='" + dimension + '\'' +
                    ", matchList=" + matchList +
                    ", keyNo='" + keyNo + '\'' +
                    ", name='" + name + '\'' +
                    ", operName='" + operName + '\'' +
                    ", startDate='" + startDate + '\'' +
                    ", status='" + status + '\'' +
                    ", no='" + no + '\'' +
                    ", creditCode='" + creditCode + '\'' +
                    '}';
        }

        /***
         *
         *匹配原因实体类
         * */
        public class Match implements Serializable {
            @JSONField(name = "Field")
            //匹配维度
            private String field;


            @JSONField(name = "Value")
            //匹配维度对应值
            private String value;

            public String getField() {
                return field;
            }

            public void setField(String field) {
                this.field = field;
            }

            public String getValue() {
                return value;
            }

            public void setValue(String value) {
                this.value = value;
            }

            @Override
            public String toString() {
                return "Match{" +
                        "field='" + field + '\'' +
                        ", value='" + value + '\'' +
                        '}';
            }
        }


    }



}
