package com.yhd.buc.foreign.pojo.po;


import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * uc_resources_closed_dimension
 * <AUTHOR>
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "uc_resources_closed_dimension")
public class ResourcesClosedDimensionPO extends BaseEntity {

    /**
     * 资源id
     */
    private String resourcesCode;

    /**
     * 编码(根据type来决定,例如type为user,这里的值则为user_code)
     */
    private String dimensionCode;

    /**
     * 维度类型
     */
    private String type;

    /**
     * 平台编码
     */
    private String platformCode;
}