package com.yhd.buc.foreign.pojo.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
    * 客户资料主表
    */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "uc_sync_customer_info")
public class UcSyncCustomerInfoPO extends BaseEntity {



    /**
     * 逻辑删除 0正常，1删除
     */
    @TableField(value = "is_delete")
    @TableLogic
    private Integer isDelete;

    /**
     * 客户编号
     */
    @TableField(value = "customer_no")
    private String customerNo;

    /**
     * 客户姓名
     */
    @TableField(value = "customer_name")
    private String customerName;

    /**
     * 客户简称
     */
    @TableField(value = "shortname")
    private String shortname;

    /**
     * 客户来源：展会收集、朋友介绍、客户转介绍、个人观察、网上搜索、电商平台、甲方指定
     */
    @TableField(value = "customer_source")
    private String customerSource;

    /**
     * 所属行业
     */
    @TableField(value = "industry")
    private String industry;

    /**
     * 公司网址
     */
    @TableField(value = "company_web")
    private String companyWeb;

    /**
     * 公司地址
     */
    @TableField(value = "address")
    private String address;

    /**
     * 行业全称
     */
    @TableField(value = "industry_name")
    private String industryName;

    /**
     * 一级行业
     */
    @TableField(value = "primary_industry")
    private String primaryIndustry;

    /**
     * 二级行业
     */
    @TableField(value = "secondary_industry")
    private String secondaryIndustry;

    /**
     * 客户类别
     */
    @TableField(value = "customer_class")
    private String customerClass;

    /**
     * 客户等级 资料客户1, 潜在客户2,意向客户3,成交客户4,长期客户5,FANS客户6
     */
    @TableField(value = "customer_grade")
    private Integer customerGrade;

    /**
     * 销售类型  国内销售0 国外销售1
     */
    @TableField(value = "sales_type")
    private Integer salesType;

    /**
     * 预估采购潜力（万元）
     */
    @TableField(value = "estimated_procurement")
    private BigDecimal estimatedProcurement;

    /**
     * 采购潜力 
     */
    @TableField(value = "purchasing_potential")
    private String purchasingPotential;

    /**
     * 设备产品
     */
    @TableField(value = "equipment_products")
    private String equipmentProducts;

    /**
     * 经度
     */
    @TableField(value = "longitude")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @TableField(value = "latitude")
    private BigDecimal latitude;

    /**
     * 长途区号
     */
    @TableField(value = "area_code")
    private String areaCode;

    /**
     * 所属城市
     */
    @TableField(value = "city")
    private String city;

    /**
     * 接受多地发货 是 1、否 0
     */
    @TableField(value = "many_places_delivery")
    private Integer manyPlacesDelivery;

    /**
     * 电商注册 否0 仅询价1 网上订购2
     */
    @TableField(value = "ecommerce_registry")
    private Integer ecommerceRegistry;

    /**
     * 人员数量
     */
    @TableField(value = "personnel_quantity")
    private Integer personnelQuantity;

    /**
     * 客户层级 系统自动根据新人员数量如下规则带出新客户层级：新人员数量*1.5 小于50 为6级，小于200为5级，小于400为4级，小于800为3级，小于1500为2级，大于1500为1 级，此规则要配置在数据字典
     */
    @TableField(value = "customer_level")
    private Integer customerLevel;

    /**
     * 含税价格属性 含税两位1 含税四位2
     */
    @TableField(value = "tax_price_attribute")
    private Integer taxPriceAttribute;

    /**
     * 地区名称
     */
    @TableField(value = "region_name")
    private String regionName;

    /**
     * 地区
     */
    @TableField(value = "region")
    private String region;

    /**
     * 建档日期
     */
    @TableField(value = "regdate")
    private Date regdate;

    /**
     * 建档人
     */
    @TableField(value = "registrant")
    private String registrant;

    /**
     * 计数
     */
    @TableField(value = "counter")
    private Integer counter;

    /**
     * 报价比例
     */
    @TableField(value = "quotation_proportion")
    private BigDecimal quotationProportion;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 注册地址
     */
    @TableField(value = "registry_address")
    private String registryAddress;

    /**
     * 法人代表
     */
    @TableField(value = "legal_person")
    private String legalPerson;

    /**
     * 企业性质
     */
    @TableField(value = "ent_nature")
    private String entNature;

    /**
     * 工商注册号
     */
    @TableField(value = "registry_no")
    private String registryNo;

    /**
     * 税务登记号
     */
    @TableField(value = "tax_id")
    private String taxId;

    /**
     * 股票代码
     */
    @TableField(value = "stock_code")
    private String stockCode;

    /**
     * 注册币别
     */
    @TableField(value = "registry_currency")
    private String registryCurrency;

    /**
     * 注册资金-万
     */
    @TableField(value = "registry_capital")
    private BigDecimal registryCapital;

    /**
     * 成立日期
     */
    @TableField(value = "registry_time")
    private Date registryTime;

    /**
     * 统一信用代码
     */
    @TableField(value = "credit_code")
    private String creditCode;

    /**
     * 联系电话
     */
    @TableField(value = "contact_phone")
    private String contactPhone;

    /**
     * 授权编号 [010000,050000,040000,070000]
     */
    @TableField(value = "authorized_no")
    private String authorizedNo;

    /**
     * 是否禁止整单分批 是 1、否 0
     */
    @TableField(value = "is_forbid_all_batches")
    private Integer isForbidAllBatches;

    /**
     * 是否禁止整行分批 是 1、否 0
     */
    @TableField(value = "is_forbid_row_batches")
    private Integer isForbidRowBatches;

    /**
     * 客户类型 客户1 供应商2 客户供应商3
     */
    @TableField(value = "customer_type")
    private Integer customerType;

    /**
     * 是否客户 是 1、否 0
     */
    @TableField(value = "is_customer")
    private Integer isCustomer;

    /**
     * 机械工程师总人数
     */
    @TableField(value = "engineers_quantity")
    private Integer engineersQuantity;

    /**
     * 预估今年营业额（万元）
     */
    @TableField(value = "estimated_turnover")
    private BigDecimal estimatedTurnover;

    /**
     * 币种
     */
    @TableField(value = "currency")
    private String currency;

    /**
     * 归属公司
     * */
    @TableField(value = "ownership_company")
    private String ownershipCompany;

    /**
     * 含税审核时间
     * */
    @TableField(value = "tax_price_audit_date")
    private LocalDateTime taxPriceAuditDate;
}