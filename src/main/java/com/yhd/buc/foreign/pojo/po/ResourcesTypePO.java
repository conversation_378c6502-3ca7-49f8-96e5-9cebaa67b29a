package com.yhd.buc.foreign.pojo.po;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * uc_resources_type
 * <AUTHOR> 平台资源类型表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "uc_resources_type")
public class ResourcesTypePO extends BaseEntity {

    /**
     * 配置id
     */
    private String configId;

    /**
     * 资源名称
     */
    private String typeName;

    /**
     * 资源说明
     */
    private String remark;

}