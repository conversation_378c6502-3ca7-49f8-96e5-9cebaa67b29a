package com.yhd.buc.foreign.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "uc_sync_region")
public class SyncRegionPO extends BaseEntity {
    //地区ID
    private String regionId;

    //地区名
    private String name;

    //父地区ID
    private String parentId;

}
