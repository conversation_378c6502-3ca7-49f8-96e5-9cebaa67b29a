package com.yhd.buc.foreign.pojo.vo.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.yhd.common.pojo.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id:QCCFuzzySearchVO.java, v 0.12022/2/24 9:45 kangrong Exp $
 * 企查查模糊搜索VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QCCFuzzySearchRequestVO extends BaseVO {
    //企业集合
    @JSONField(name = "Result")
    private List<Enterprise> result;

    @JsonProperty("Status")
    private  String status;

    public List<Enterprise> getResult() {
        return result;
    }

    public void setResult(List<Enterprise> result) {
        this.result = result;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 企业类
     * */
    public class Enterprise implements Serializable {
        //KeyNo
        private String keyNo;

        //企业名称
        private String name;

        //统一社会信用代码
        private String creditCode;

        //成立日期
        private String startDate;

        //法定代表人姓名
        private String operName;

        //状态
        private String status;

        //注册号
        private String no;

        public String getKeyNo() {
            return keyNo;
        }

        @JSONField(name = "KeyNo")
        public void setKeyNo(String keyNo) {
            this.keyNo = keyNo;
        }

        public String getName() {
            return name;
        }

        @JSONField(name = "Name")
        public void setName(String name) {
            this.name = name;
        }

        public String getCreditCode() {
            return creditCode;
        }

        @JSONField(name = "CreditCode")
        public void setCreditCode(String creditCode) {
            this.creditCode = creditCode;
        }

        public String getStartDate() {
            return startDate;
        }

        @JSONField(name = "StartDate")
        public void setStartDate(String startDate) {
            this.startDate = startDate;
        }

        public String getOperName() {
            return operName;
        }

        @JSONField(name = "OperName")
        public void setOperName(String operName) {
            this.operName = operName;
        }

        public String getStatus() {
            return status;
        }

        @JSONField(name = "Status")
        public void setStatus(String status) {
            this.status = status;
        }

        public String getNo() {
            return no;
        }

        @JSONField(name = "NO")
        public void setNo(String no) {
            this.no = no;
        }

        @Override
        public String toString() {
            return "Enterprise{" +
                    "keyNo='" + keyNo + '\'' +
                    ", name='" + name + '\'' +
                    ", creditCode='" + creditCode + '\'' +
                    ", startDate='" + startDate + '\'' +
                    ", operName='" + operName + '\'' +
                    ", status='" + status + '\'' +
                    ", no='" + no + '\'' +
                    '}';
        }
    }


}
