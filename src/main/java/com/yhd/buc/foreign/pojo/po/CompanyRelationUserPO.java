package com.yhd.buc.foreign.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户企业关联表
 *
 * <AUTHOR>
 * @since 2022-08-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("uc_company_relation_user")
public class CompanyRelationUserPO extends BaseEntity {

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 企业编码
     */
    private String companyCode;

    /**
     * 'enable'(启用),'disable'（未启用）
     */
    private String status;

}