package com.yhd.buc.foreign.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version Id:InteriorResourcesResponseVO.java, v 0.12023/4/7 9:27 kangrong Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "资源参数")
public class InteriorResourcesResponseVO extends BaseVO {

        @Schema(description = "资源名称")
        private String resourcesName;

        @Schema(description = "资源路径")
        private String resourcesPath;

        @Schema(description = "资源编码")
        private String resourcesCode;

}
