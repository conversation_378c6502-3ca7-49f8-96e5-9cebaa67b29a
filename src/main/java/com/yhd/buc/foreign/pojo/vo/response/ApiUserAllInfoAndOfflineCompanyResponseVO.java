package com.yhd.buc.foreign.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version Id:ApiUserAllInfoAndOfflineCompanyResponseVO.java, v 0.12023/5/5 18:26 kangrong Exp $
 * 获取全部用户信息以及线下企业信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "获取全部用户信息以及线下企业信息")
public class ApiUserAllInfoAndOfflineCompanyResponseVO extends BaseVO {

    @Schema(description = "线上用户全部信息")
    private ApiUserAllInfoResponseVO apiUserAllInfoResponseVO;

    @Schema(description = "线下企业信息信息")
    private OfflineCompanyInfoResponseVO offlineCompanyInfoResponseVO;
}
