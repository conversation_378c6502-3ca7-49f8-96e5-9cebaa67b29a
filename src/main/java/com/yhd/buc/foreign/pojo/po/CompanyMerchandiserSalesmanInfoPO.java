package com.yhd.buc.foreign.pojo.po;


import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 业务员与跟单员详细信息表(CompanyMerchandiserSalesmanInfo)实体类
 *
 * <AUTHOR>
 * @since 2023-03-13 11:58:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "uc_company_merchandiser_salesman_info")
public class CompanyMerchandiserSalesmanInfoPO extends BaseEntity {
    /**
     * 工号
     */
    private String employeeCode;
    /**
     * 邮箱
     */
    private String email;
    /**
     * qq
     */
    private String qq;

    /**
     * 企业微信二维码
     * */
    private String enterpriseWechatCodeUrl;

}

