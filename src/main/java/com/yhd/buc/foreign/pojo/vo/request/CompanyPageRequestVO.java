package com.yhd.buc.foreign.pojo.vo.request;
import com.yhd.buc.foreign.constant.RouteConstant;
import com.yhd.common.pojo.vo.PageRequestVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version Id:RequestCompanyVO.java, v 0.12022/7/2 10:14 kangrong Exp $
 * 企业分页参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "企业分页参数")
public class CompanyPageRequestVO extends PageRequestVO {
    //企业编码
    @NotEmpty(message = RouteConstant.NOT_NULL_MESSAGE_TEMPLATE)
    @Schema(description = "企业编码",required = true)
    private String companyCode;


    //用户名
    @Schema(description = "用户名")
    private String username;

    //手机
    @Schema(description = "手机")
    private String mobile;


    //邮箱
    @Schema(description = "邮箱")
    private String email;
}
