package com.yhd.buc.foreign.pojo.vo.request;

import com.yhd.buc.foreign.constant.RouteConstant;
import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version Id:UserPlatformCodeRequestVO.java, v 0.12023/3/19 17:33 kangrong Exp $
 * 用户平台编码参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "用户平台编码参数")
public class UserPlatformCodeRequestVO extends BaseVO {
    @NotEmpty(message = RouteConstant.NOT_NULL_MESSAGE_TEMPLATE)
    @Schema(description = "平台编码",required = true)
    private String platformCode;


    //"用户编码
    @NotEmpty(message = RouteConstant.NOT_NULL_MESSAGE_TEMPLATE)
    @Schema(description = "用户编码",required = true)
    private  String userCode;
}
