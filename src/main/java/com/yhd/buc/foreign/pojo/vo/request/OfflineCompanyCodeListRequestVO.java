package com.yhd.buc.foreign.pojo.vo.request;

import com.yhd.buc.foreign.constant.RouteConstant;
import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id:RequestCompanyVO.java, v 0.12022/7/2 10:14 kangrong Exp $
 * 企业编码集合VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OfflineCompanyCodeListRequestVO extends BaseVO {
    //企业编码
    @Schema(description = "erp企业编码",required = true)
    private String erpCompanyCode;

}
