package com.yhd.buc.foreign.pojo.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
    * 月结信息表
    */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "uc_sync_monthly_settle")
public class UcSyncMonthlySettlePO extends BaseEntity {

    /**
     * 逻辑删除 0正常，1删除
     */
    @TableField(value = "is_delete")
    @TableLogic
    private Integer isDelete;

    /**
     * 客户编号
     */
    @TableField(value = "customer_no")
    private String customerNo;

    /**
     * 子公司标识 东莞010000,怡惠购040000,苏州050000,工品070000
     */
    @TableField(value = "company_code")
    private String companyCode;

    /**
     * 应收结算日
     */
    @TableField(value = "settlement_date")
    private Integer settlementDate;

    /**
     * 信用额度
     */
    @TableField(value = "credit_line")
    private BigDecimal creditLine;

    /**
     * 结算方式 预付全款-A001，票到付款-JF00，货到付款-JF01，、月底结-JF02，月结30天-JF03，月结60天-JF04，月结90天-JF05，现金-JF11、分期付款-JF23
     */
    @TableField(value = "settlement_type")
    private String settlementType;

    /**
     * 付款方式 1现金 2电汇 3当期支票 4超期支票 5银行汇款 6企业承兑 7快递代收现金 8业务担保
     */
    @TableField(value = "payment_type")
    private Integer paymentType;

    /**
     * 对账业务期间从
     */
    @TableField(value = "reconc_biz_start")
    private Integer reconcBizStart;

    /**
     * 对账业务期间至
     */
    @TableField(value = "reconc_biz_end")
    private Integer reconcBizEnd;

    /**
     * 对账时间从
     */
    @TableField(value = "reconc_start")
    private Integer reconcStart;

    /**
     * 对账时间至
     */
    @TableField(value = "reconc_end")
    private Integer reconcEnd;

    /**
     * 收到发票时间从
     */
    @TableField(value = "recpt_inv_start")
    private Integer recptInvStart;

    /**
     * 收到发票时间至
     */
    @TableField(value = "recpt_inv_end")
    private Integer recptInvEnd;

    /**
     * 付款时间从
     */
    @TableField(value = "payment_start")
    private Integer paymentStart;

    /**
     * 付款时间至
     */
    @TableField(value = "payment_end")
    private Integer paymentEnd;
}