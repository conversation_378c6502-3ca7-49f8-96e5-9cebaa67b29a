package com.yhd.buc.foreign.pojo.vo.request;

import com.yhd.buc.foreign.constant.RouteConstant;
import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version Id:ThirdPlatformQueryCompanyVO.java, v 0.12022/2/22 17:18 kangrong Exp $
 * 企业查询信息VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "第三方平台查询")
public class ThirdQueryCompanyRequestVO extends BaseVO {
    //企业名称
    @NotEmpty(message = RouteConstant.NOT_NULL_MESSAGE_TEMPLATE)
    @Schema(description = "企业名称",required = true)
    private String companyName;

    //类型(模糊查询:fuzzyQuery、精确查询preciseQuery)
    @NotEmpty(message = RouteConstant.NOT_NULL_MESSAGE_TEMPLATE)
    @Schema(description = "类型(模糊查询:fuzzyQuery、精确查询preciseQuery)",required = true)
    private String type;

}
