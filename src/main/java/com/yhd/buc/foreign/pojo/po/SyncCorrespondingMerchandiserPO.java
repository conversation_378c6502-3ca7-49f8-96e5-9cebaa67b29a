package com.yhd.buc.foreign.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *业务员对应跟单表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("uc_sync_corresponding_merchandiser")
public class SyncCorrespondingMerchandiserPO extends BaseEntity {
    //"跟单编号"
    private String empNo;

    //"业务员工号"
    private String salesNo;

    //"业务员名称"
    private String salesName;

    //"部门编号"
    private String deptNo;

    //"部门名称"
    private String deptName;

    //"员工状态 在职0 离职1 停职2 访客3 操作员999"
    private Integer status;

    //"序号"
    private Integer autoid;

   // "名称"
    private String postname;

   // "移动电话"
    private String mobilePhone;

    //  "子公司标识 东莞010000,怡惠购040000,苏州050000,工品070000"
    private String companyCode;
}
