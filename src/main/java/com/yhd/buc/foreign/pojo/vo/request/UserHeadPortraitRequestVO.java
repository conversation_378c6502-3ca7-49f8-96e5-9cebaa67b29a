package com.yhd.buc.foreign.pojo.vo.request;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version Id:UserHeadPortraitVO.java, v 0.12022/6/22 15:50 kangrong Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "头像参数")
public class UserHeadPortraitRequestVO extends BaseVO {
    //用户编码不能为空
    @NotEmpty(message = "用户编码不能为空")
    @Schema(description = "用户编码",required = true)
    private  String userCode;
    //头像url
    @NotEmpty(message = "头像不能为空")
    @Schema(description = "头像Url",required = true)
    private  String fileUrl;
}
