package com.yhd.buc.foreign.pojo.vo.response;

import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version Id:ApiUserAdminResponse.java, v 0.12023/6/13 10:46 kangrong Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "管理员集合")
public class ApiUserAdminResponse extends BaseVO {
    private String userCode;

    private String erpCompany;
}
