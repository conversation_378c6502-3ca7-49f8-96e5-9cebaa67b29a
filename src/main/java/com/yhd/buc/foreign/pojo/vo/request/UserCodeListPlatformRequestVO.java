package com.yhd.buc.foreign.pojo.vo.request;

import com.yhd.buc.foreign.constant.RouteConstant;
import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id:UserCodeVO.java, v 0.12022/6/11 9:24 kangrong Exp $
 * 用户编码集合VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "用户编码集合参数")
public class UserCodeListPlatformRequestVO extends BaseVO {
    //用户编码集合
    @Schema(description = "用户编码集合",required = true)
    private List<String> userCodeList;

    //平台标识
    @NotEmpty(message = RouteConstant.NOT_NULL_MESSAGE_TEMPLATE)
    @Schema(description = "平台标识",required = true)
    private String platformCode;
}
