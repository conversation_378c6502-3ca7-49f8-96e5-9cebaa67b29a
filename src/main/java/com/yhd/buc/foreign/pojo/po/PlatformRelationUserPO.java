package com.yhd.buc.foreign.pojo.po;


import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 平台-用户关联表(在平台登录过)(PlatformUserRelation)实体类
 *
 * <AUTHOR>
 * @since 2023-02-21 20:36:53
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "uc_platform_relation_user")
public class PlatformRelationUserPO extends BaseEntity {

    /**
     * 用户编码
     */
    private String userCode;
    /**
     * 平台标识
     */
    private String platformCode;

    /**
     * ip
     */
    private String ip;

}

