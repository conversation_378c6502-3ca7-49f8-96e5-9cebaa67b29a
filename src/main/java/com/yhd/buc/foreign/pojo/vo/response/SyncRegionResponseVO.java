package com.yhd.buc.foreign.pojo.vo.response;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class SyncRegionResponseVO extends BaseVO {
    //地区ID
    @Schema(description = "地区ID")
    private String regionId;

    //地区名
    @Schema(description = "地区名")
    private String name;

    //父地区ID
    @Schema(description = "父地区ID")
    private String parentId;

}
