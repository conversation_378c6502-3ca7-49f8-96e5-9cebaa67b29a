package com.yhd.buc.foreign.pojo.vo.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @version Id:ResponseOfflineCompanyInfoVO.java, v 0.12022/8/14 15:14 kangrong Exp $
 * 返回的线下企业信息VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "返回的线下企业信息VO")
public class OfflineCompanyInfoByErpCompanyResponseVO extends BaseVO {
    //erp企业编码
    @Schema(description = "erp企业编码")
    private String erpCompanyCode;

    //企业名称
    @Schema(description = "企业名称")
    private String companyName;

    /**
     * 结算方式 预付全款-A001，票到付款-JF00，货到付款-JF01，、月底结-JF02，月结30天-JF03，月结60天-JF04，月结90天-JF05，现金-JF11、分期付款-JF23
     */
    @Schema(description = "结算方式 预付全款-A001，票到付款-JF00，货到付款-JF01，、月底结-JF02，月结30天-JF03，月结60天-JF04，月结90天-JF05，现金-JF11、分期付款-JF23")
    private String settlementMethod;

    //交易状况 正常交易1 暂停交易2
    @Schema(description = "交易状况 正常交易1 暂停交易2")
    private Integer tradingStatus;


    //跟单员工号
    @JsonInclude
    @Schema(description = "跟单员工号")
    private String merchandiserJobNumber;

    //跟单员名称
    @JsonInclude
    @Schema(description = "跟单员名称")
    private String merchandiserName;

    //跟单员电话
    @JsonInclude
    @Schema(description = "跟单员电话")
    private String merchandiserMobile;

    //跟单员邮箱
    @JsonInclude
    @Schema(description = "跟单员邮箱")
    private String merchandiserEmail;

    //跟单员QQ
    @JsonInclude
    @Schema(description = "跟单员QQ")
    private String merchandiserQq;

    //跟单员QQ
    @JsonInclude
    @Schema(description = "跟单员岗位")
    private String merchandiserPost;


    /**
     * 业务员工号
     */
    @Schema(description = "业务员工号")
    @JsonInclude
    private String operatorJobNumber;

    /**
     * 业务员名字
     */
    @Schema(description = "业务员名字")
    @JsonInclude
    private String operatorName;

    /**
     * 业务员手机
     *
     */
    @Schema(description = "业务员手机")
    @JsonInclude
    private String operatorMobile;


    /**
     * 业务员邮箱
     */
    @Schema(description = "业务员邮箱")
    @JsonInclude
    private String operatorEmail;

    /**
     * 业务员qq
     */
    @Schema(description = "业务员qq")
    @JsonInclude
    private String operatorQq;

    //业务员岗位
    @JsonInclude
    @Schema(description = "业务员岗位")
    private String operatorPost;


    @Schema(description = "含税价格属性 含税两位1 含税四位2")
    @JsonInclude
    private Integer taxPriceAttribute;

    @Schema(description = "含税价格审核时间")
    @JsonInclude
    private LocalDateTime taxPriceAuditDate;

    @Schema(description = "归属公司 东莞-DGYHD 苏州-SZYHD")
    @JsonInclude
    private String ownershipCompany;


    @Schema(description = "折扣等级 A-1 B-2 C-3 D-4 B+-5 C+-6 D+-7 S-8（4 7 3 6 2 5 1 8从小到大）")
    @JsonInclude
    private int discountLevel;

    //子公司标识
    @Schema(description = "子公司标识（子公司标识 东莞010000,怡惠购040000,苏州050000,工品070000）")
    private String companyIdentity;


    /**
     * 企业地址
     *
     */
    @Schema(description = "企业地址")
    @JsonInclude
    private String companyAddress;


    @Schema(description = "跟单员集合")
    private List<MerchandiserResponseVO> merchandiserResponseVOList;
}
