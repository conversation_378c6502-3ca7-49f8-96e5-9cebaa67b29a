package com.yhd.buc.foreign.pojo.vo.request;
import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version Id:RequestCompanyVO.java, v 0.12022/7/2 10:14 kangrong Exp $
 * 企业VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "企业参数")
public class CompanyCodeRequestVO extends BaseVO {
    //企业编码
    @NotEmpty(message = "企业编码不能为空")
    @Schema(description = "企业编码不能为空",required = true)
    private String companyCode;
}
