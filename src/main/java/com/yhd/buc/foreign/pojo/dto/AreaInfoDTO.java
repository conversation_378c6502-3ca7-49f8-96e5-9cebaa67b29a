package com.yhd.buc.foreign.pojo.dto;


import com.yhd.common.pojo.dto.BaseDTO;
import lombok.*;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version Id: AreaInfoDTO.java, v 0.1 2025/7/21 11:00 JiangYuHong Exp $
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AreaInfoDTO extends BaseDTO {

    //地区名
    private String name;

    //地区ID
    private int regionId;

    //父地区ID
    private int parentId;

    private int zipcode;

    public AreaInfoDTO(String name, int regionId, int parentId) {
        this.name = name;
        this.regionId = regionId;
        this.parentId = parentId;
        this.zipcode = 0;
    }
}
