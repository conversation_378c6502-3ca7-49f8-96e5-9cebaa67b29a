package com.yhd.buc.foreign.pojo.po;


import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 角色-企业关联表(RoleRelationCompany)实体类
 *
 * <AUTHOR>
 * @since 2023-02-28 21:06:59
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "uc_role_relation_company")
public class RoleRelationCompanyPO extends BaseEntity {

    /**
     * 角色编码
     */
    private String roleCode;
    /**
     * 企业编码
     */
    private String companyCode;
    /**
     * 平台标识
     */
    private String platformCode;



}

