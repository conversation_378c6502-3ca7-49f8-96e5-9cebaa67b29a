package com.yhd.buc.foreign.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 账户登录表
 *
 * <AUTHOR>
 * @since 2022-08-25
 */

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("uc_account")
public class AccountPO extends BaseEntity {

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 账户
     */
    private String account;

    /**
     * 'mobile'（手机）,'email'（邮箱）
     */
    private String type;

    /**
     * 密码
     */
    private String password;

    /**
     * 盐值
     */
    private String salt;

    /**
     * 'disable'（未启用）,'enable'（启用）
     */
    private String status;


    /**
     * 创建时间
     */
    private LocalDateTime createdDate;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedDate;

    /**
     * 更新人
     */
    private String updatedBy;

}