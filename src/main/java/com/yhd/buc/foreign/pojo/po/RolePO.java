package com.yhd.buc.foreign.pojo.po;


import com.baomidou.mybatisplus.annotation.TableName;
import com.yhd.common.pojo.po.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 角色表(Role)实体类
 *
 * <AUTHOR>
 * @since 2023-02-20 19:39:17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "uc_role")
public class RolePO extends BaseEntity {

    /**
     * 角色名称
     */
    private String roleName;
    /**
     * 角色编码(平台做前缀)
     */
    private String roleCode;
    /**
     * 状态
     */
    private String status;
    /**
     * 配置标识
     */
    private String configId;
    /**
     * 角色类型：user:用户类型；company：企业类型（用户类型可以继承企业类型，企业类型不能继承用户类型）
     */
    private String roleType;
    /**
     * 关联企业角色
     */
    private String relationRole;
    /**
     * 备注说明
     */
    private String remark;



}

