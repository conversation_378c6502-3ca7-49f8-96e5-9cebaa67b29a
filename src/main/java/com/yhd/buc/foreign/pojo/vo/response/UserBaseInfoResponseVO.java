package com.yhd.buc.foreign.pojo.vo.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id:ResponseUserInfoVO.java, v 0.12022/6/22 17:53 kangrong Exp $
 * 返回用户基础信息VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "返回用户基础信息VO")
public class UserBaseInfoResponseVO extends BaseVO {
    private  String  id;
    //用户编码
    @Schema(description = "用户编码")
    private  String  userCode;

    //用户名
    @Schema(description = "用户名")
    private  String  userName;

    //邮箱
    @JsonInclude
    @Schema(description = "邮箱")
    private  String  email;

    //邮箱是否绑定
    @JsonInclude
    @Schema(description = "邮箱是否绑定")
    private String emailCheck;

    //手机是否绑定
    @JsonInclude
    @Schema(description = "手机是否绑定")
    private String mobileCheck;

    //手机
    @JsonInclude
    @Schema(description = "手机")
    private  String  mobile;

    //职位
    @JsonInclude
    @Schema(description = "职位")
    private  String  occupation;

    //性别：female：女；male：男；secrecy：保密
    @JsonInclude
    @Schema(description = "性别：female：女；male：男；secrecy：保密")
    private  String  gender;

    //生日
    @JsonInclude
    @Schema(description = "生日")
    private  String  birthday;

    //爱好
    @JsonInclude
    @Schema(description = "爱好")
    private  String  hobby;

    //是否是测试账户
    @Schema(description = "是否是测试账户")
    private  String   testUser;

    //用户头像
    @Schema(description = "用户头像")
    private  String  avatarPath;

    //qq是否绑定
    @Schema(description = "qq是否绑定")
    private String qqCheck;

    //qq名字
    @Schema(description = "qq名字")
    private  String  qqName;

    //qqOpenId
    @Schema(description = "qqOpenId")
    private  String  qqOpenId;

    //微信OpenId
    @Schema(description = "微信OpenId")
    private  String  wechatOpenId;

    //微信是否绑定
    @Schema(description = "微信是否绑定")
    private String wechatCheck;

    //微信名字
    @Schema(description = "微信名字")
    private  String  wechatName;

    //企业编码
    @JsonInclude
    @Schema(description = "企业编码")
    private  String  companyCode;

    //企业名称
    @Schema(description = "企业名称")
    private  String  companyName;

    @Schema(description = "企业状态normal(正常)、audit(认证审核)，filing建档 processing(加入企业中)")
    private String companyStatus;

    @Schema(description = "注册平台来源")
    private String platformSource;

    @Schema(description = "注册时间")
    private LocalDateTime registerDate;

    @Schema(description = "'enabled'(启用),'disabled'（未启用）")
    private String status;
}
