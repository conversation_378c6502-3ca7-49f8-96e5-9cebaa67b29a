package com.yhd.buc.foreign.pojo.vo.request;
import com.yhd.buc.foreign.constant.RouteConstant;
import com.yhd.common.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version Id:RequestCompanyVO.java, v 0.12022/7/2 10:14 kangrong Exp $
 * 企业VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "企业平台参数")
public class CompanyCodePlatformRequestVO extends BaseVO {
    //企业编码
    @NotEmpty(message = "企业编码不能为空")
    @Schema(description = "企业编码不能为空",required = true)
    private String companyCode;


    //平台标识
    @NotEmpty(message = RouteConstant.NOT_NULL_MESSAGE_TEMPLATE)
    @Schema(description = "平台标识",required = true)
    private String platformCode;
}
