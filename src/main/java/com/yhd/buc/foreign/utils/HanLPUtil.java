package com.yhd.buc.foreign.utils;


import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.dictionary.CustomDictionary;
import com.hankcs.hanlp.seg.Segment;
import com.yhd.common.util.LogUtils;
import lombok.Getter;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version Id: HanLPUtil.java, v 0.1 2025/8/1 16:05 JiangYuHong Exp $
 */
@Getter
@Component
public class HanLPUtil {

    private static final Logger logger = LogUtils.getLogger(HanLPUtil.class.getName());

    private Segment segment;

    /**
     * 初始化
     */
    @PostConstruct
    public void init() {

        // 加载自定义词典
        loadCustomDictionary();
        // 初始化 Segment，开启所需识别功能
        segment = HanLP.newSegment()
                .enableCustomDictionary(true)
                .enablePlaceRecognize(true);
        logger.info("HanLPUtil init completed");
    }

    /**
     * 加载自定义词典
     */
    private void loadCustomDictionary() {
        logger.info("开始加载 HanLP 自定义词典");

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(
                Objects.requireNonNull(getClass().getResourceAsStream("/custom_dict.txt"))))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (!line.trim().isEmpty()) {
                    CustomDictionary.add(line.trim(), "ns");
                }
            }
            logger.info("HanLP 自定义词典加载完成");
        } catch (Exception e) {
            logger.error("无法加载自定义词典：{}", e.getMessage());
        }
    }
}
