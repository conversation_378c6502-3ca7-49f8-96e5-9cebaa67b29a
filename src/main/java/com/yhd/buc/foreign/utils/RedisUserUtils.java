package com.yhd.buc.foreign.utils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yhd.buc.foreign.constant.RedisConstant;
import com.yhd.buc.foreign.service.api.PlatformService;
import com.yhd.common.util.CommonConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.redis.service.RedisService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version Id:RedisUserUtils.java, v 0.12022/9/19 10:46 kangrong Exp $
 */
@Component
public class RedisUserUtils {

    private RedisUserUtils() {
    }

    /**
     * 日志参数
     */
    private static final Logger logger = LogUtils.getLogger();

    @Resource
    private RedisService redisService;

    @Resource
    private PlatformService platformService;



    /**
     * 删除用户信息键
     * @param userCode 用户编码
     *
     * */
    public void  deleteRedisUserInfo(String userCode){
        logger.info("redis remove userBaseInfo key is :{} userCode is :{}",RedisConstant.YHD_SERVICE_BUC_USER_BASE_INFO,userCode);
        Long delUserBaseResult = redisService.hdel(RedisConstant.YHD_SERVICE_BUC_USER_BASE_INFO, userCode);
        logger.info("redis remove userBaseInfo key is :{} result is :{}",RedisConstant.YHD_SERVICE_BUC_USER_BASE_INFO,delUserBaseResult);

        //删除用户总信息
        deleteRedisUserAllInfo(userCode);
    }

    /**
     * 删除用户控制资源键
     * @param platformCode 平台编码
     * @param userCodeList 影响的用户
     * */
    public void  deleteRedisUserControl(String platformCode,List<String> userCodeList){
        String key =  RedisConstant.YHD_SERVICE_BUC_RESOURCES_CONTROL + platformCode;
        logger.info("redis removeControl  key is :{}",key);
        redisService.del(RedisConstant.YHD_SERVICE_BUC_RESOURCES_CONTROL + platformCode);
        //删除用户总信息
        for (String userCode : userCodeList) {
            deleteRedisUserAllInfo(userCode);
        }
    }

    /**
     * 删除用户信息键
     * @param userCode 用户编码
     *
     * */
    public void  deleteRedisUserRole(String userCode){
        List<Object> list = platformService.queryPlatformCodeList();
        for (Object platform :list) {
            //删除角色键
            String roleValue =  platform + CommonConstant.DASH + userCode;
            logger.info("redis remove userRole key is :{} value is :{}",RedisConstant.YHD_SERVICE_BUC_USER_ROLE_INFO,roleValue);
            redisService.hdel(RedisConstant.YHD_SERVICE_BUC_USER_ROLE_INFO,roleValue);

            //删除用户键
            String userAllInfoKey =  RedisConstant.YHD_SERVICE_BUC_USER_ALL_INFO + platform;
            logger.info("redis remove userAllInfoKey key is :{} userCode is :{}",userAllInfoKey,userCode);
            Long userAllInfoResult = redisService.hdel(userAllInfoKey, userCode);
            logger.info("redis remove userAllInfoKey key is :{} result is :{}",userAllInfoKey,userAllInfoResult);
        }
    }

    /**
     * 删除用户全部信息键
     * @param userCode 用户编码
     *
     * */
    public void  deleteRedisUserAllInfo(String userCode){
        List<Object> list = platformService.queryPlatformCodeList();
        for (Object platform :list) {
            String userAllInfoKey =  RedisConstant.YHD_SERVICE_BUC_USER_ALL_INFO + platform;
            logger.info("redis remove userAllInfoKey key is :{} userCode is :{}",userAllInfoKey,userCode);
            Long userAllInfoResult = redisService.hdel(userAllInfoKey, userCode);
            logger.info("redis remove userAllInfoKey key is :{} result is :{}",userAllInfoKey,userAllInfoResult);
        }
    }


    /**
     * 删除线上企业键
     * @param companyCode 企业编码
     * */
    public void  deleteRedisCompanyKey(String companyCode){
        logger.info("redis remove deleteRedisCompanyKey key is :{} userCode is :{}",RedisConstant.YHD_SERVICE_BUC_ONLINE_COMPANY_INFO,companyCode);
        Long result = redisService.hdel(RedisConstant.YHD_SERVICE_BUC_ONLINE_COMPANY_INFO ,companyCode);
        logger.info("redis remove deleteRedisCompanyKey key is :{} result is :{}",RedisConstant.YHD_SERVICE_BUC_ONLINE_COMPANY_INFO,result);
    }


    /**
     * 删除线下企业键
     * @param companyCode 企业编码
     * */
    public void  deleteRedisOfflineCompanyKey(String companyCode,String platformCode){
        String key =  RedisConstant.YHD_SERVICE_BUC_OFFLINE_COMPANY_INFO + platformCode;
        logger.info("redis remove deleteRedisOfflineCompanyKey key is :{} userCode is :{}",key,companyCode);
        Long result = redisService.hdel(key,companyCode);
        logger.info("redis remove deleteRedisOfflineCompanyKey key is :{} result is :{}",key,result);
    }


    /**
     * 重载
     * 删除线下企业键
     * @param companyCode 企业编码
     * */
    public void  deleteRedisOfflineCompanyKey(String companyCode){
        List<Object> list = platformService.queryPlatformCodeList();
        list.stream().map(m->RedisConstant.YHD_SERVICE_BUC_OFFLINE_COMPANY_INFO+m.toString()).forEach(e-> redisService.hdel(e,companyCode));
    }



    /**
     * 删除用户控制资源键
     * */
    public void  deleteRedisUserControl(String userCode){
        List<Object> platformCodeList = platformService.queryPlatformCodeList();
        platformCodeList.forEach(e ->{
            String key =  RedisConstant.YHD_SERVICE_BUC_RESOURCES_CONTROL + e;
            logger.info("redis removeControl  key is :{}",key);
            Long result = redisService.hdel(key, com.yhd.buc.foreign.constant.CommonConstant.USER + CommonConstant.DASH + userCode);
            logger.info("redisService hdel key is :{} result is :{}",key,result);
        });



    }
}
