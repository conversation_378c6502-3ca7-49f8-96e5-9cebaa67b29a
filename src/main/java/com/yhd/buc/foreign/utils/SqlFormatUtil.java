package com.yhd.buc.foreign.utils;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.LambdaUtils;
import com.baomidou.mybatisplus.core.toolkit.support.ColumnCache;
import com.baomidou.mybatisplus.core.toolkit.support.LambdaMeta;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import org.apache.ibatis.reflection.property.PropertyNamer;

import java.util.Map;
import java.util.Objects;

/**
 * sql格式化工具
 * 使用全文检索函数需要先建全文索引:
 * ALTER TABLE table_name ADD FULLTEXT INDEX index_name(table_field) WITH PARSER ngram;
 *
 * <AUTHOR>
 * @version Id: SqlFormatUtil.java, v 0.1 2023/3/3 15:58 yaozicong Exp $
 */
public class SqlFormatUtil {

    private SqlFormatUtil() {

    }

    public static <T> String getFullTextSegment(SFunction<T, ?> sFunction) {
        return getFullTextSegment(sFunction, FullTextMode.NATURAL_LANGUAGE_MODE);
    }

    public static <T> String getFullTextSegment(SFunction<T, ?> sFunction, FullTextMode fullTextMode) {
        LambdaMeta meta = LambdaUtils.extract(sFunction);
        String fieldName = PropertyNamer.methodToProperty(meta.getImplMethodName());
        Map<String, ColumnCache> columnMap = LambdaUtils.getColumnMap(meta.getInstantiatedClass());
        // 不能为空
        Objects.requireNonNull(columnMap);
        ColumnCache columnCache = columnMap.get(LambdaUtils.formatKey(fieldName));
        return CharSequenceUtil.format(fullTextMode.getTpl(), columnCache.getColumn());
    }

    public enum FullTextMode {
        NATURAL_LANGUAGE_MODE("MATCH({}) AGAINST({0} IN NATURAL LANGUAGE MODE)"),
        BOOLEAN_MODE("MATCH({}) AGAINST({0} IN BOOLEAN MODE)");

        private final String tpl;

        FullTextMode(String tpl) {
            this.tpl = tpl;
        }

        public String getTpl() {
            return tpl;
        }
    }
}
