package com.yhd.buc.foreign.utils;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Stopwatch;
import com.hankcs.hanlp.seg.common.Term;
import com.yhd.buc.foreign.dao.SyncRegionDAO;
import com.yhd.buc.foreign.pojo.dto.AreaInfoDTO;
import com.yhd.buc.foreign.pojo.po.SyncRegionPO;
import com.yhd.buc.foreign.pojo.vo.response.SmartAddressResponse;
import com.yhd.common.util.LogUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version Id: AddressParserUtil.java, v 0.1 2025/7/21 09:48 JiangYuHong Exp $
 */
@Component
public class AddressParserUtil {

    private static final Logger logger = LogUtils.getLogger(AddressParserUtil.class.getName());

    // 需要过滤掉收货地址中的常用说明字符，排除干扰词(字符长的必须在前面)
    private static final String[] FILTER_WORDS = {
            "详细地址", "地址", "所在地区", "收货人", "收件人", "收货", "邮编",
            "联系电话", "电话", "手机号码", "手机号", "手机", "姓名", "身份证号码",
            "身份证号", "身份证", "：", ":", "；", ";", "，", ",", "。", "."
    };

    // 预编译得正则表达式
    private static final Pattern WHITESPACE_PATTERN = Pattern.compile("\\s+");
    private static final Pattern MULTIPLE_SPACES_PATTERN = Pattern.compile(" {2,}");
    private static final Pattern PHONE_DASH_PATTERN = Pattern.compile("0?(\\d{3})-(\\d{4})-(\\d{4})([-_]\\d{2,})");
    private static final Pattern ID_PATTERN = Pattern.compile("(?i)\\d{18}|\\d{17}X");
    private static final Pattern MOBILE_PATTERN = Pattern.compile("\\d{7,11}[\\-_]\\d{2,6}|\\d{7,11}|\\d{3,4}-\\d{6,8}");
    private static final Pattern POSTCODE_PATTERN = Pattern.compile("\\d{6}");

    // 地区等级常量
    private static final String PROVINCE = "province";
    private static final String CITY = "city";
    private static final String REGION = "region";
    // 缓存过期时间（毫秒）- 1天
    private static final long CACHE_EXPIRE_TIME = 24 * 60 * 60 * 1000L;
    // 区域映射数据
    private final Map<Integer, AreaInfoDTO> provinceById = new HashMap<>();
    private final Map<String, List<AreaInfoDTO>> provinceByName = new HashMap<>();
    private final Map<Integer, AreaInfoDTO> cityById = new HashMap<>();
    private final Map<String, List<AreaInfoDTO>> cityByName = new HashMap<>();
    private final Map<Integer, List<AreaInfoDTO>> cityByPid = new HashMap<>();
    private final Map<Integer, AreaInfoDTO> regionById = new HashMap<>();
    private final Map<String, List<AreaInfoDTO>> regionByName = new HashMap<>();
    private final Map<Integer, List<AreaInfoDTO>> regionByPid = new HashMap<>();
    // "ns" 是HanLP地点词的标记
    public static final String NS = "ns";
    @Resource
    private SyncRegionDAO syncRegionDAO;

    private boolean initialized = false;
    private long lastLoadTime = 0;

    @Resource
    private HanLPUtil hanLPUtil;

    /**
     * 分离手机号(座机)，身份证号，姓名，地址等信息
     *
     * @param info Address对象
     * @param str  待解析的字符串
     */
    private static void decompose(SmartAddressResponse info, String str) {

        // 将所有换行符去除
        str = str.replaceAll("[\\r\\n]", " ");

        // 1. 过滤掉收货地址中的常用说明字符，排除干扰词
        for (String word : FILTER_WORDS) {
            str = str.replace(word, " ");
        }

        // 2. 多个空白字符(包括空格\r\n\t)换成一个空格
        str = WHITESPACE_PATTERN.matcher(str).replaceAll(" ").trim();

        // 3. 去除手机号码中的短横线 如0136-3333-6666 主要针对苹果手机
        str = PHONE_DASH_PATTERN.matcher(str).replaceAll("$1$2$3$4");

        // 4. 提取中国境内身份证号码
        Matcher idMatcher = ID_PATTERN.matcher(str);
        if (idMatcher.find()) {
            String idNumber = idMatcher.group();
            str = str.replace(idNumber, "");
            info.setIdNumber(idNumber.toUpperCase());
        }

        // 5. 提取11位手机号码或者7位以上座机号，支持虚拟号的提取
        Matcher mobileMatcher = MOBILE_PATTERN.matcher(str);
        if (mobileMatcher.find()) {
            String mobile = mobileMatcher.group();
            str = str.replace(mobile, "");
            info.setMobile(mobile);
        }

        // 6. 提取6位邮编 邮编也可用后面解析出的省市区地址从数据库匹配出
        Matcher postcodeMatcher = POSTCODE_PATTERN.matcher(str);
        if (postcodeMatcher.find()) {
            String postcode = postcodeMatcher.group();
            str = str.replace(postcode, "");
            info.setPostCode(postcode);
        }

        // 再次把2个及其以上的空格合并成一个，并首位TRIM
        str = MULTIPLE_SPACES_PATTERN.matcher(str).replaceAll(" ").trim();

        // 7. 按照空格切分 长度长的为地址 短的为姓名 因为不是基于自然语言分析，所以采取统计学上高概率的方案
        String[] parts = str.split(" ");

        if (parts.length == 0) {
            return;
        }

        String name = parts[0];
        for (String part : parts) {
            if (part.length() < name.length()) {
                name = part;
            }
        }

        if (parts.length == 1) {
            info.setAddress(parts[0]);
            return;
        }

        info.setName(name);
        String address = str.replace(name, "").trim();
        info.setAddress(address);
    }

    /**
     * 检查缓存是否过期
     */
    private boolean isCacheExpired() {
        return System.currentTimeMillis() - lastLoadTime > CACHE_EXPIRE_TIME;
    }

    /**
     * 初始化区域数据
     */
    private void initializeAreaData() {

        if (initialized && !isCacheExpired()) {
            return;
        }

        try {

            //查询所有
            List<SyncRegionPO> syncRegionPOList = syncRegionDAO.selectList(new LambdaQueryWrapper<>());

            // 清空现有数据
            clearAllMaps();

            // 提取省级数据
            syncRegionPOList.stream().filter(e -> e.getParentId().equals("0")).forEach(e -> provinceById.put(Integer.parseInt(e.getRegionId()), new AreaInfoDTO(e.getName(), Integer.parseInt(e.getRegionId()), 0)));

            // 省份名称映射
            for (AreaInfoDTO province : provinceById.values()) {
                provinceByName.computeIfAbsent(province.getName(), k -> new ArrayList<>()).add(province);

                // 按照省级提取市级数据
                syncRegionPOList.stream().filter(e -> province.getRegionId() == Integer.parseInt(e.getParentId())).forEach(e -> cityById.put(Integer.parseInt(e.getRegionId()), new AreaInfoDTO(e.getName(), Integer.parseInt(e.getRegionId()), province.getRegionId())));
            }

            // 城市名称映射
            for (AreaInfoDTO city : cityById.values()) {
                cityByName.computeIfAbsent(city.getName(), k -> new ArrayList<>()).add(city);
                cityByPid.computeIfAbsent(city.getParentId(), k -> new ArrayList<>()).add(city);

                // 按照市级提取区县级数据
                syncRegionPOList.stream().filter(e -> city.getRegionId() == Integer.parseInt(e.getParentId())).forEach(e -> regionById.put(Integer.parseInt(e.getRegionId()), new AreaInfoDTO(e.getName(), Integer.parseInt(e.getRegionId()), city.getRegionId())));
            }
            // 区县名称映射
            for (AreaInfoDTO region : regionById.values()) {
                regionByName.computeIfAbsent(region.getName(), k -> new ArrayList<>()).add(region);
                regionByPid.computeIfAbsent(region.getParentId(), k -> new ArrayList<>()).add(region);
            }

            initialized = true;
            lastLoadTime = System.currentTimeMillis();

        } catch (Exception e) {
            logger.error("初始化区域数据失败 :{}", e.toString());
        }

    }

    /**
     * 清空所有映射数据
     */
    private void clearAllMaps() {
        provinceById.clear();
        provinceByName.clear();
        cityById.clear();
        cityByName.clear();
        cityByPid.clear();
        regionById.clear();
        regionByName.clear();
        regionByPid.clear();
    }

    /**
     * 收货地址解析
     *
     * @param str 待解析的地址字符串
     * @return 解析后的Address对象
     */
    public SmartAddressResponse smart(String str) {

        try {
            // 记录开始时间
            Stopwatch stopwatch = Stopwatch.createStarted();
            initializeAreaData();

            SmartAddressResponse info = new SmartAddressResponse();
            decompose(info, str);
            parse(info);

            // 提取街道地址
            extractStreet(info, info.getProvince());
            extractStreet(info, info.getCity());
            extractStreet(info, info.getRegion());

            logger.info("smart address completed. cos:{} ", stopwatch.elapsed(TimeUnit.MILLISECONDS));
            return info;

        } catch (DataAccessException e) {
            logger.error("数据库访问异常: {}", e.getMessage(), e);
            return new SmartAddressResponse();
        } catch (IllegalArgumentException e) {
            logger.warn("输入参数异常: {}", e.getMessage());
            return new SmartAddressResponse();
        } catch (Exception e) {
            logger.error("地址解析异常: {}", e.getMessage(), e);
            return new SmartAddressResponse();
        }

    }

    /**
     * 提取街道地址
     */
    private void extractStreet(SmartAddressResponse info, String keyword) {
        if (StringUtils.isNotEmpty(keyword) && info.getAddress().contains(keyword)) {
            int index = info.getAddress().lastIndexOf(keyword);
            String street = info.getAddress().substring(index + keyword.length()).trim();
            info.setStreet(street);
        }
    }

    /**
     * 智能解析出省市区+街道地址
     *
     * @param address Address对象
     */
    private void parse(SmartAddressResponse address) {

        List<String> arr = parerArea(address.getAddress());

        // 处理区县级
        processRegion(address, arr);

        // 处理市级
        processCity(address, arr);

        // 处理省级
        processProvince(address, arr);
    }

    /**
     * 处理省级
     */
    private void processProvince(SmartAddressResponse address, List<String> arr) {
        if (address.getProvince() == null || address.getProvince().isEmpty()) {
            for (String p : arr) {
                List<AreaInfoDTO> provinceList = provinceByName.get(p);
                if (provinceList != null && !provinceList.isEmpty()) {
                    AreaInfoDTO province = provinceList.get(0);
                    applyProvinceInfo(address, province);

                    getAddressByPid(address, province.getRegionId(), CITY, arr);
                    getAddressByPid(address, province.getRegionId(), REGION, arr);
                    break;
                }
            }
        }
    }

    /**
     * 处理市级
     */
    private void processCity(SmartAddressResponse address, List<String> cArr) {
        if (address.getCity() == null || address.getCity().isEmpty()) {
            for (String c : cArr) {
                List<AreaInfoDTO> cityList = cityByName.get(c);
                if (cityList != null && !cityList.isEmpty()) {
                    AreaInfoDTO city = cityList.get(0);
                    applyCityInfo(address, city);
                    getAddressById(address, city.getParentId(), PROVINCE);
                    getAddressByPid(address, city.getRegionId(), REGION, cArr);
                    break;
                }
            }
        }
    }

    /**
     * 处理区县级
     */
    private void processRegion(SmartAddressResponse address, List<String> rArr) {
        for (String r : rArr) {
            List<AreaInfoDTO> regionList = regionByName.get(r);
            if (regionList == null || regionList.isEmpty()) {
                continue;
            }

            if (regionList.size() == 1) {
                applyRegionInfo(address, regionList.get(0));
                getAddressById(address, regionList.get(0).getParentId(), CITY);
                break;
            }

            for (AreaInfoDTO region : regionList) {
                applyRegionInfo(address, region);
                getAddressById(address, region.getParentId(), CITY);

                if (rArr.contains(address.getCity())) {
                    return;
                }
            }
        }
    }

    /**
     * 应用区县级信息
     */
    private void applyRegionInfo(SmartAddressResponse address, AreaInfoDTO region) {
        address.setRegion(region.getName());
        address.setRegionId(region.getRegionId());
        address.setPostCode(String.valueOf(region.getZipcode()));
    }

    /**
     * 应用市级信息
     */
    private static void applyCityInfo(SmartAddressResponse address, AreaInfoDTO city) {
        address.setCity(city.getName());
        address.setCityId(city.getRegionId());
        address.setPostCode(String.valueOf(city.getZipcode()));
    }

    /**
     * 应用省级信息
     */
    private static void applyProvinceInfo(SmartAddressResponse address, AreaInfoDTO province) {
        address.setProvince(province.getName());
        address.setProvinceId(province.getRegionId());
        address.setPostCode(String.valueOf(province.getZipcode()));
    }

    /**
     * 地区解析
     */
    private List<String> parerArea(String text) {

        List<String> matches = new ArrayList<>();
        List<Term> seg = hanLPUtil.getSegment().seg(text);
        for (Term term : seg) {
            if (term.nature.toString().startsWith(NS)) {
                matches.add(term.word);
            }
        }

        return matches;
    }

    /**
     * 根据id获取地址信息
     */
    private void getAddressById(SmartAddressResponse address, int id, String rank) {
        switch (rank) {
            case PROVINCE:
                Optional.ofNullable(provinceById.get(id)).ifPresent(provinceInfo -> {
                    applyProvinceInfo(address, provinceInfo);
                });
                break;
            case CITY:
                Optional.ofNullable(cityById.get(id)).ifPresent(cityInfo -> {
                    applyCityInfo(address, cityInfo);
                    getAddressById(address, cityInfo.getParentId(), PROVINCE);
                });
                break;
            case REGION:
                Optional.ofNullable(regionById.get(id)).ifPresent(regionInfo -> {
                    applyRegionInfo(address, regionInfo);
                    getAddressById(address, regionInfo.getParentId(), CITY);
                });
                break;
            default:
                break;
        }
    }

    /**
     * 根据pid获取下一级行政地址信息
     */
    private void getAddressByPid(SmartAddressResponse address, int pid, String rank, List<String> arr) {
        if (address == null || arr == null || arr.isEmpty()) {
            return;
        }

        if (CITY.equals(rank) && StringUtils.isBlank(address.getCity())) {
            updateAddressFields(address, cityByPid.get(pid), arr, true);
        }

        if (REGION.equals(rank) && StringUtils.isBlank(address.getRegion())) {
            updateAddressFields(address, regionByPid.get(pid), arr, false);
        }
    }

    /**
     * 更新地址字段
     */
    private void updateAddressFields(SmartAddressResponse address, List<AreaInfoDTO> areaList, List<String> arr, boolean isCity) {
        if (areaList == null || areaList.isEmpty()) {
            return;
        }

        for (String addr : arr) {
            for (AreaInfoDTO info : areaList) {
                if (StringUtils.contains(info.getName(), addr)) {
                    if (isCity) {
                        address.setCity(info.getName());
                        address.setCityId(info.getRegionId());
                    } else {
                        address.setRegion(info.getName());
                        address.setRegionId(info.getRegionId());
                    }
                    address.setPostCode(String.valueOf(info.getZipcode()));
                    return;
                }
            }
        }
    }

}
