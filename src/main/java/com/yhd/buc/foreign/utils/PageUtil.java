package com.yhd.buc.foreign.utils;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id:PageUtil.java, v 0.12022/6/21 16:21 kangrong Exp $
 */
public class PageUtil {

    private static final String LIST = "list";

    private PageUtil() {
    }

    public static <E, T> PageInfo<T> of(List<E> poList, List<T> voList) {
        return of(poList, voList, true);
    }

    /**
     * 分页
     *
     * @param poList po集合
     * @param voList vo集合
     * @param isPage 分页信息
     * @param <E>    po类型
     * @param <T>    vo类型
     * @return
     */
    public static <E, T> PageInfo<T> of(List<E> poList, List<T> voList, boolean isPage) {
        PageInfo<T> pageInfoVo = PageInfo.of(voList);
        if (isPage) {
            PageInfo<E> pageInfo = PageInfo.of(poList);
            BeanUtils.copyProperties(pageInfo, pageInfoVo, PageUtil.LIST);
        }
        return pageInfoVo;
    }
}
