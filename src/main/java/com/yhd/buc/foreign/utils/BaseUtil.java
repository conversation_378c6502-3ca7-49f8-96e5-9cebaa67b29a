package com.yhd.buc.foreign.utils;

import cn.hutool.core.util.IdUtil;
import com.yhd.buc.foreign.constant.CommonConstant;
import com.yhd.common.pojo.po.BaseEntity;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version Id: BaseUtil.java, v 0.1 2023/12/1 11:30 fenggang Exp $
 */
public class BaseUtil {
    private BaseUtil() {
    }


    /**
     * 创建时公用参数赋值
     */
    public static <T extends BaseEntity> void setCreatedParams(T t) {
        if (StringUtils.isBlank(t.getId())){
            t.setId(IdUtil.simpleUUID());
        }
        t.setCreatedBy(CommonConstant.ADMIN);
        t.setUpdatedBy(CommonConstant.ADMIN);
        t.setCreatedDate(LocalDateTime.now());
        t.setUpdatedDate(LocalDateTime.now());
    }


    /**
     * 更新时公用参数赋值
     */
    public static <T extends BaseEntity> void setUpdateParams(T t) {
        t.setUpdatedBy(CommonConstant.ADMIN);
        t.setUpdatedDate(LocalDateTime.now());
    }

}
