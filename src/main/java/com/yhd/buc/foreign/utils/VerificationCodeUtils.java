package com.yhd.buc.foreign.utils;

import com.yhd.buc.foreign.constant.RedisConstant;
import com.yhd.common.util.LogUtils;
import com.yhd.redis.service.RedisService;
import org.slf4j.Logger;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version Id:VerificationCodeUtils.java, v 0.12022/5/25 9:36 kangrong Exp $
 */
public class VerificationCodeUtils {
    /**
     * 日志参数
     */
    private static final Logger logger = LogUtils.getLogger();

    private VerificationCodeUtils() {
    }

    /**
     * @param linkNumber 联系方式（电话或者邮箱）
     * @param authenticationType 验证类型（如注册，忘记密码，绑定用户）
     * @param redisClientService redis
     * */
    public static String generator (RedisService redisClientService, String linkNumber, String authenticationType){
        String key = null;
        key = RedisConstant.YHD_BUC_FOREIGN + RedisConstant.REGISTRATION_VERIFICATION_CODE+ authenticationType + linkNumber;
        //把键清除
        redisClientService.del(key);
        //利用哈希值生成验证码
        String verificationCode = null;
        int hash = linkNumber.hashCode();
        //加密
        int encryption = 20220524;
        //用hash异或上加密得到生成第一次加密结果
        //这个生成的加过永远是固定的如果加密码不动的情况下
        long result = hash ^ encryption;
        //利用获得当前时间再次加密得出结果
        long time = System.currentTimeMillis();
        result = result ^ time;
        //取后六位
        long code = result % 1000000;
        //此时随机验证码生成完成
        //此时会出现生成的验证码为负值，利用三目表达式进行解决
        code = code < 0 ? -code : code;
        //此时还会出现第二个问题当生成000656这种只会显示656不会补零所以我们现在要解决当位数不够在前补零
        //解决思路利用数据结构的算法，利用数组，将得到的验证码长度作为数组的角码，在拼接字符就可以完成补零
        //先把得到的验证码变成字符串
        String codeStr = code + "";
        //再把code的字符长度取出,作为数组的角标
        int len = codeStr.length();
        String[] patch = {"000000", "00000", "0000", "000", "00", "0", ""};
        //验证有多少为需要补
        String patch1 = patch[len];

        verificationCode = patch1 + codeStr;
        logger.info("verificationCodeUtils parameter is :{},verificationCode is:{}",linkNumber,verificationCode);
        Boolean redisResult = redisClientService.set(key, verificationCode, Duration.ofMinutes(10));
        logger.info("redis stored verification code result is :{} ",redisResult);
        return verificationCode;
    }
}
