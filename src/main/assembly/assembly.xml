<assembly>
    <id>assemblyId</id>
    <!-- 最终打包成一个用于发布的zip文件 -->
    <formats>
        <format>zip</format>
    </formats>

    <fileSets>
        <!-- 打包jar文件 -->
        <fileSet>
            <directory>${project.build.directory}</directory>
            <outputDirectory>.</outputDirectory>
            <includes>
                <include>*.jar</include>
            </includes>
        </fileSet>

        <!-- 把项目相关的启动脚本，打包进zip文件的bin目录 -->
        <fileSet>
            <directory>${project.basedir}/src/main/assembly/bin</directory>
            <outputDirectory>bin</outputDirectory>
            <directoryMode>0755</directoryMode> <!--目录执行权限-->
            <fileMode>0755</fileMode><!--文件执行权限-->
            <includes>
                <include>*</include>
            </includes>
        </fileSet>
    </fileSets>
</assembly>