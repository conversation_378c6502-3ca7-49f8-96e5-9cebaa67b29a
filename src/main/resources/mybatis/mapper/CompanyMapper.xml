<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhd.buc.foreign.dao.CompanyDAO">
    <resultMap id="BaseResultMap" type="com.yhd.buc.foreign.pojo.po.CompanyPO">
        <id column="id" jdbcType="CHAR" property="id"/>
        <result column="company_code" jdbcType="CHAR" property="companyCode"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="erp_company_code" jdbcType="VARCHAR" property="erpCompanyCode"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="attestation_status" jdbcType="VARCHAR" property="attestationStatus"/>
        <result column="registration_form_url" jdbcType="VARCHAR" property="registrationFormUrl"/>
        <result column="business_license_url" jdbcType="VARCHAR" property="businessLicenseUrl"/>
        <result column="linkname" jdbcType="VARCHAR" property="linkname"/>
        <result column="linkphone" jdbcType="VARCHAR" property="linkphone"/>
        <result column="linkemail" jdbcType="VARCHAR" property="linkemail"/>
        <result column="occupation" jdbcType="VARCHAR" property="occupation"/>
        <result column="department" jdbcType="VARCHAR" property="department"/>
        <result column="is_test" jdbcType="VARCHAR" property="isTest"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="created_date" jdbcType="TIMESTAMP" property="createdDate"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="updated_date" jdbcType="TIMESTAMP" property="updatedDate"/>
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy"/>
    </resultMap>

    <resultMap id="AdminResultMap" type="com.yhd.buc.foreign.pojo.vo.response.ApiUserAdminResponse">
        <id column="user_code" jdbcType="CHAR" property="userCode"/>
        <result column="erp_company_code" jdbcType="CHAR" property="erpCompany"/>
    </resultMap>

    <!-- 表名 -->
    <sql id="tableName">
        uc_company
    </sql>



    <select id="getAdminListByErpCompany" resultMap="AdminResultMap">
        SELECT
        b.erp_company_code,
        a.user_code
        FROM
        uc_company_administrator a
        LEFT JOIN uc_company b ON b.company_code = a.company_code
        WHERE
        b.erp_company_code IN
        <foreach collection="erpCompanyCodeList" item="item" open="(" separator="," close=")">
            (
            #{item}
            )
        </foreach>
    </select>
</mapper>