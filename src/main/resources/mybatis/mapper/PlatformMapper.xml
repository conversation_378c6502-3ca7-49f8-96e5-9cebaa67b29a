<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhd.buc.foreign.dao.PlatformDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yhd.buc.foreign.pojo.po.PlatformPO">
        <id column="id" property="id"/>
        <result column="platform_code" jdbcType="VARCHAR" javaType="String" property="platformCode"/>
        <result column="home_page" jdbcType="VARCHAR" javaType="String" property="homePage"/>
        <result column="user_center" jdbcType="VARCHAR" javaType="String" property="userCenter"/>
        <result column="platform_name" jdbcType="VARCHAR" javaType="String" property="platformName"/>
        <result column="resource_ids" jdbcType="VARCHAR" javaType="String" property="resourceIds"/>
        <result column="description" jdbcType="VARCHAR" javaType="String" property="description"/>
        <result column="client_secret" jdbcType="VARCHAR" javaType="String" property="clientSecret"/>
        <result column="scope" jdbcType="VARCHAR" javaType="String" property="scope"/>
        <result column="authorized_grant_types" jdbcType="VARCHAR" javaType="String" property="authorizedGrantTypes"/>
        <result column="redirect_uri" jdbcType="VARCHAR" javaType="String" property="redirectUri"/>
        <result column="access_token_validity" jdbcType="BIGINT" javaType="Integer" property="accessTokenValidity"/>
        <result column="refresh_token_validity" jdbcType="BIGINT" javaType="Integer" property="refreshTokenValidity"/>
        <result column="autoapprove" jdbcType="VARCHAR" javaType="String" property="autoapprove"/>
        <result column="company_code" jdbcType="VARCHAR" javaType="String" property="companyCode"/>
        <result column="status" jdbcType="VARCHAR" javaType="String" property="status"/>
        <result column="created_date" jdbcType="TIMESTAMP" property="createdDate"/>
        <result column="created_by" jdbcType="VARCHAR" javaType="String" property="createdBy"/>
        <result column="updated_date" jdbcType="TIMESTAMP" property="updatedDate"/>
        <result column="updated_by" jdbcType="VARCHAR" javaType="String" property="updatedBy"/>
    </resultMap>

    <!-- 表名 -->
    <sql id="tableName">
        uc_platform
    </sql>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,home_page,user_center
        , platform_code, platform_name, description, resource_ids, client_secret, scope, authorized_grant_types, redirect_uri, access_token_validity, refresh_token_validity, autoapprove, status,company_code, created_date, created_by, updated_date, updated_by
    </sql>


    <select id="getPlatformInfoCodeListBySubsidiaryCode" resultType="string">
        select
        platform_code
        from
        <include refid="tableName"/>
        where `company_code` = #{companyCode}  AND status = 'enabled'
    </select>

</mapper>
