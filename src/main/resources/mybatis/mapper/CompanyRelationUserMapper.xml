<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhd.buc.foreign.dao.CompanyRelationUserDAO">
  <resultMap id="BaseResultMap" type="com.yhd.buc.foreign.pojo.po.CompanyRelationUserPO">
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="user_code" jdbcType="CHAR" property="userCode" />
    <result column="company_code" jdbcType="CHAR" property="companyCode" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="created_date" jdbcType="TIMESTAMP" property="createdDate" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_date" jdbcType="TIMESTAMP" property="updatedDate" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <!-- 表名 -->
  <sql id="tableName">
    uc_company_relation_user
  </sql>

  <sql id="Base_Column_List">
    id, user_code, company_code, `status`, created_date, created_by, updated_date, updated_by
  </sql>



  <select id="getUserCodeListByCompanyCode" resultType="string">
    select
    user_code
    from
    <include refid="tableName"/>
    where `company_code` = #{companyCode}  AND status = 'enabled'
  </select>


</mapper>