<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhd.buc.foreign.dao.UserInfoDAO">
  <resultMap id="BaseResultMap" type="com.yhd.buc.foreign.pojo.po.UserInfoPO">
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="user_code" jdbcType="CHAR" property="userCode" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="department" jdbcType="VARCHAR" property="department" />
    <result column="occupation" jdbcType="VARCHAR" property="occupation" />
    <result column="gender" jdbcType="OTHER" property="gender" />
    <result column="birthday" jdbcType="VARCHAR" property="birthday" />
    <result column="hobby" jdbcType="VARCHAR" property="hobby" />
    <result column="avatar_path" jdbcType="VARCHAR" property="avatarPath" />
    <result column="is_test" jdbcType="OTHER" property="isTest" />
    <result column="business_source" jdbcType="OTHER" property="businessSource" />
    <result column="register_source" jdbcType="OTHER" property="registerSource" />
    <result column="platform_source" jdbcType="VARCHAR" property="platformSource" />
    <result column="status" jdbcType="OTHER" property="status" />
    <result column="created_date" jdbcType="TIMESTAMP" property="createdDate" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_date" jdbcType="TIMESTAMP" property="updatedDate" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>


  <resultMap id="ResultMap" type="com.yhd.buc.foreign.pojo.vo.response.JoinCompanyUseListResponseVO">
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="user_code" jdbcType="CHAR" property="userCode" />
    <result column="username" jdbcType="VARCHAR" property="userName" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="occupation" jdbcType="VARCHAR" property="occupation" />
    <result column="admin" jdbcType="VARCHAR" property="admin" />
  </resultMap>

  <select id="selectJoinCompanyMember" resultMap="ResultMap">
select a.* from (SELECT DISTINCT
    t.id,
    t.user_code,
    t.username,
    t.mobile,
    t.email,
    t.occupation,
    t.avatar_path,
    CASE WHEN t2.user_code is NULL THEN 'false' ELSE 'true' END AS admin
FROM
    uc_user_info t
    LEFT JOIN uc_company_relation_user t1 ON (t1.user_code = t.user_code)
    LEFT JOIN uc_company_administrator t2 ON (t2.user_code = t.user_code)
    LEFT JOIN uc_role_relation_user t3 on (t3.user_code = t.user_code)
WHERE
    (t1.company_code = #{companyPageRequestVO.companyCode} AND t1.STATUS = 'enabled' AND t.STATUS = 'enabled'
      <if test="roleCode!=null and roleCode!=''">
          AND t3.role_code = #{roleCode}
      </if>
      <if test="companyPageRequestVO.username!=null and companyPageRequestVO.username!=''">
          AND t.username  LIKE CONCAT('%', #{companyPageRequestVO.username},'%')
      </if>
      <if test="companyPageRequestVO.mobile!=null and companyPageRequestVO.mobile!=''">
          AND t.mobile  LIKE CONCAT('%', #{companyPageRequestVO.mobile},'%')
      </if>
      <if test="companyPageRequestVO.email!=null and companyPageRequestVO.email!=''">
          AND t.email  LIKE CONCAT('%', #{companyPageRequestVO.email},'%')
      </if>
    )
ORDER BY
   admin DESC
	) a
  </select>

    <select id="selectUserInfoByUserName" parameterType="java.lang.String" resultType="com.yhd.buc.foreign.pojo.vo.response.UserInfoByUserNameResponseVO">
    SELECT
	user_code,
	username AS user_name
    FROM
	uc_user_info
    WHERE
	`status` = 'enabled'
       <if test="userName != null and userName !='' ">
            AND username LIKE CONCAT(#{userName},'%')
       </if>
	LIMIT 200
    </select>

</mapper>