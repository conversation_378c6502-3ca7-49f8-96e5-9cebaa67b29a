<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">  
    <modelVersion>4.0.0</modelVersion>  
    <groupId>com.yhd</groupId>  
    <artifactId>yhd-buc-foreign</artifactId>  
    <version>1.0.0</version>  
    <name>yhd-buc-foreign</name>  
    <parent> 
        <!--http://wiki.dgyiheda.com:8090/pages/viewpage.action?pageId=63051591-->  
        <groupId>com.yhd.springcloud</groupId>  
        <artifactId>yhd-spring-cloud-parent</artifactId>  
        <version>3.0.5.1</version> 
    </parent>  
    <properties> 
        <maven.compiler.source>8</maven.compiler.source>  
        <maven.compiler.target>8</maven.compiler.target>  
        <sonar.projectKey>yhd-service-user_yhd-buc-foreign_AYODPBJ23AQmYsbqZYLB</sonar.projectKey>  
        <sonar.qualitygate.wait>true</sonar.qualitygate.wait>  
        <sonar.moduleKey>${project.artifactId}</sonar.moduleKey>  
        <sonar.branch.name>dev2.0</sonar.branch.name> 
    </properties>  
    <dependencies>
        <dependency>
            <groupId>com.hankcs</groupId>
            <artifactId>hanlp</artifactId>
            <version>portable-1.8.4</version>
        </dependency>
        <!-- nacos config  -->  
        <dependency> 
            <groupId>com.alibaba.cloud</groupId>  
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId> 
        </dependency>  
        <!-- nacos discovery  -->  
        <dependency> 
            <groupId>com.alibaba.cloud</groupId>  
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId> 
        </dependency>  
        <!--web 模块-->  
        <dependency> 
            <groupId>org.springframework.boot</groupId>  
            <artifactId>spring-boot-starter-web</artifactId> 
        </dependency>  
        <!--undertow容器-->  
        <dependency> 
            <groupId>org.springframework.boot</groupId>  
            <artifactId>spring-boot-starter-undertow</artifactId> 
        </dependency>  
        <!-- 数据库连接及多租户 -->  
        <!--http://wiki.dgyiheda.com:8090/pages/viewpage.action?pageId=63052099-->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-database-starter</artifactId> 
        </dependency>  
        <dependency> 
            <groupId>mysql</groupId>  
            <artifactId>mysql-connector-java</artifactId> 
        </dependency>  
        <!-- 操作日志 -->  
        <!--http://wiki.dgyiheda.com:8090/pages/viewpage.action?pageId=63052465-->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-log-starter</artifactId> 
        </dependency>  
        <!-- redis -->  
        <!--http://wiki.dgyiheda.com:8090/pages/viewpage.action?pageId=63052515-->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-redis-starter</artifactId> 
        </dependency>  
        <!-- 分布式锁 及幂等性处理  -->  
        <!--http://wiki.dgyiheda.com:8090/pages/viewpage.action?pageId=63052304-->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-lock-starter</artifactId> 
        </dependency>  
        <!-- validator 参数校验 -->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-validator-starter</artifactId> 
        </dependency>  
        <!-- xxs攻击过滤  -->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-xss-starter</artifactId> 
        </dependency>  
        <!-- feign 依赖 -->  
        <!--http://wiki.dgyiheda.com:8090/pages/viewpage.action?pageId=63052267-->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-feign-starter</artifactId> 
        </dependency>  
        <!-- sentinel 依赖 -->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-sentinel-starter</artifactId> 
        </dependency>  
        <!-- springdoc 依赖 -->  
        <!--http://wiki.dgyiheda.com:8090/pages/viewpage.action?pageId=63052621-->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-springdoc-starter</artifactId> 
        </dependency>  
        <!-- 灰度路由 -->  
        <!--http://wiki.dgyiheda.com:8090/pages/viewpage.action?pageId=63052282-->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-gray-starter</artifactId> 
        </dependency>  
        <!-- seata -->  
        <!--        <dependency>
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-seata-starter</artifactId> 
        </dependency>  -->  
        <!--xxl-job-->  
        <!--http://wiki.dgyiheda.com:8090/pages/viewpage.action?pageId=63052285-->  
        <!--http://wiki.dgyiheda.com:8090/pages/viewpage.action?pageId=63050072-->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-job-starter</artifactId> 
        </dependency>  
        <!--二方包-->  
        <!--第三方jar-->  
        <!--lang3-->  
        <dependency> 
            <groupId>org.apache.commons</groupId>  
            <artifactId>commons-lang3</artifactId>  
            <version>3.12.0</version> 
        </dependency>  
        <!-- 分页 -->  
        <dependency> 
            <groupId>com.github.pagehelper</groupId>  
            <artifactId>pagehelper-spring-boot-starter</artifactId> 
        </dependency>  
        <!--发送第三方请求url-->  
        <dependency> 
            <groupId>com.dtflys.forest</groupId>  
            <artifactId>forest-spring-boot-starter</artifactId>  
            <version>1.5.24</version> 
        </dependency>  
        <!-- ID生成器 -->  
        <dependency> 
            <groupId>com.yhd</groupId>  
            <artifactId>yhd-sequence-starter</artifactId> 
        </dependency>  
        <!--mybatisPlus-->  
        <dependency> 
            <groupId>com.github.yulichang</groupId>  
            <artifactId>mybatis-plus-join</artifactId>  
            <version>1.3.3</version> 
        </dependency>  
        <!-- 网关包 -->  
        <dependency> 
            <groupId>com.yhd.springboot</groupId>  
            <artifactId>authorization-spring-boot-starter</artifactId>  
            <version>2.1.0</version> 
        </dependency> 
    </dependencies>  
    <build> 
        <plugins> 
            <!--springboot打包插件导入先于assembly插件，便于打包-->  
            <plugin> 
                <groupId>org.springframework.boot</groupId>  
                <artifactId>spring-boot-maven-plugin</artifactId> 
            </plugin>  
            <plugin> 
                <groupId>org.apache.maven.plugins</groupId>  
                <artifactId>maven-assembly-plugin</artifactId>  
                <version>3.3.0</version>  
                <executions> 
                    <execution> 
                        <configuration> 
                            <appendAssemblyId>false</appendAssemblyId>  
                            <descriptors> 
                                <descriptor>src/main/assembly/assembly.xml</descriptor> 
                            </descriptors> 
                        </configuration>  
                        <id>assemblyId</id>  
                        <phase>package</phase>  
                        <goals> 
                            <goal>single</goal> 
                        </goals> 
                    </execution> 
                </executions> 
            </plugin>
            <!-- 排除打包过滤文件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>txt</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
        </plugins> 
    </build>  
    <distributionManagement> 
        <repository> 
            <id>releases</id>  
            <url>http://nexus.dgyiheda.com:8081/nexus/content/repositories/releases/</url> 
        </repository>  
        <snapshotRepository> 
            <id>snapshots</id>  
            <name>snapshots</name>  
            <url>http://nexus.dgyiheda.com:8081/nexus/content/repositories/snapshots/</url> 
        </snapshotRepository> 
    </distributionManagement> 
</project>
