sonarqube-check:
  image: maven:3.6.3-jdk-11
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - mvn verify -U org.sonarsource.scanner.maven:sonar-maven-plugin:3.9.1.2184:sonar -Dsonar.host.url=http://**********:9000 -Dsonar.gitlab.project_id=$CI_PROJECT_ID -Dsonar.gitlab.commit_sha=$CI_COMMIT_SHA -Dsonar.gitlab.ref_name=$CI_COMMIT_REF_NAME
    #    清理制品
    - mvn clean
  allow_failure: true
  only:
    - branches

#sonar_preview:
#  image: maven:3.6.3-jdk-11
#  script:
#    - COMMITTER=$(git log -1 --format=%cE)
#      echo ${COMMITTER}
#
#    - if [ $? -eq 0 ]; then
#      echo "do something for auto_test here."
#      echo "auto_test over."
#    - fi
#  except:
#    - master
#
#sonar_analyze:
#  image:  maven:3.6.3-jdk-11
#  variables:
#    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"
#    GIT_DEPTH: "0"
#  cache:
#    key: "${CI_JOB_NAME}"
#  script:
#    #    - mvn dependency:purge-local-repository
#    - mvn verify -U org.sonarsource.scanner.maven:sonar-maven-plugin:3.9.1.2184:sonar -Dsonar.host.url=http://**********:9000 -Dsonar.gitlab.project_id=$CI_PROJECT_ID -Dsonar.gitlab.commit_sha=$CI_COMMIT_SHA -Dsonar.gitlab.ref_name=$CI_COMMIT_REF_NAME
#    - if [ $? -eq 0 ]; then
#      echo "sonarqube code-analyze-preview over."
#    - fi
#  except:
#    - master